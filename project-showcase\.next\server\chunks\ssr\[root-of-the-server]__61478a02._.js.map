{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\n// Types for cart functionality\ninterface Project {\n  id: string;\n  title: string;\n  titleAr: string;\n  price: number;\n  category: 'app' | 'website';\n  image: string;\n}\n\ninterface CartItem {\n  project: Project;\n  quantity: number;\n}\n\ninterface CartState {\n  items: CartItem[];\n  total: number;\n}\n\ntype CartAction =\n  | { type: 'ADD_ITEM'; payload: Project }\n  | { type: 'REMOVE_ITEM'; payload: string }\n  | { type: 'UPDATE_QUANTITY'; payload: { id: string; quantity: number } }\n  | { type: 'CLEAR_CART' }\n  | { type: 'LOAD_CART'; payload: CartItem[] };\n\nconst cartReducer = (state: CartState, action: CartAction): CartState => {\n  switch (action.type) {\n    case 'ADD_ITEM': {\n      const existingItem = state.items.find(item => item.project.id === action.payload.id);\n      \n      if (existingItem) {\n        const updatedItems = state.items.map(item =>\n          item.project.id === action.payload.id\n            ? { ...item, quantity: item.quantity + 1 }\n            : item\n        );\n        return {\n          items: updatedItems,\n          total: updatedItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n        };\n      }\n      \n      const newItems = [...state.items, { project: action.payload, quantity: 1 }];\n      return {\n        items: newItems,\n        total: newItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    }\n    \n    case 'REMOVE_ITEM': {\n      const newItems = state.items.filter(item => item.project.id !== action.payload);\n      return {\n        items: newItems,\n        total: newItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    }\n    \n    case 'UPDATE_QUANTITY': {\n      const updatedItems = state.items.map(item =>\n        item.project.id === action.payload.id\n          ? { ...item, quantity: Math.max(0, action.payload.quantity) }\n          : item\n      ).filter(item => item.quantity > 0);\n      \n      return {\n        items: updatedItems,\n        total: updatedItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    }\n    \n    case 'CLEAR_CART':\n      return { items: [], total: 0 };\n    \n    case 'LOAD_CART':\n      return {\n        items: action.payload,\n        total: action.payload.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    \n    default:\n      return state;\n  }\n};\n\ninterface CartContextType {\n  state: CartState;\n  addItem: (project: Project) => void;\n  removeItem: (projectId: string) => void;\n  updateQuantity: (projectId: string, quantity: number) => void;\n  clearCart: () => void;\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\nexport const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(cartReducer, { items: [], total: 0 });\n\n  // Load cart from localStorage on mount\n  useEffect(() => {\n    const savedCart = localStorage.getItem('cart');\n    if (savedCart) {\n      try {\n        const cartItems = JSON.parse(savedCart);\n        dispatch({ type: 'LOAD_CART', payload: cartItems });\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save cart to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cart', JSON.stringify(state.items));\n  }, [state.items]);\n\n  const addItem = (project: Project) => {\n    dispatch({ type: 'ADD_ITEM', payload: project });\n  };\n\n  const removeItem = (projectId: string) => {\n    dispatch({ type: 'REMOVE_ITEM', payload: projectId });\n  };\n\n  const updateQuantity = (projectId: string, quantity: number) => {\n    dispatch({ type: 'UPDATE_QUANTITY', payload: { id: projectId, quantity } });\n  };\n\n  const clearCart = () => {\n    dispatch({ type: 'CLEAR_CART' });\n  };\n\n  return (\n    <CartContext.Provider value={{ state, addItem, removeItem, updateQuantity, clearCart }}>\n      {children}\n    </CartContext.Provider>\n  );\n};\n\nexport const useCart = () => {\n  const context = useContext(CartContext);\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AA8BA,MAAM,cAAc,CAAC,OAAkB;IACrC,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAY;gBACf,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;gBAEnF,IAAI,cAAc;oBAChB,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACnC,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACjC;4BAAE,GAAG,IAAI;4BAAE,UAAU,KAAK,QAAQ,GAAG;wBAAE,IACvC;oBAEN,OAAO;wBACL,OAAO;wBACP,OAAO,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;oBACxF;gBACF;gBAEA,MAAM,WAAW;uBAAI,MAAM,KAAK;oBAAE;wBAAE,SAAS,OAAO,OAAO;wBAAE,UAAU;oBAAE;iBAAE;gBAC3E,OAAO;oBACL,OAAO;oBACP,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;gBACpF;YACF;QAEA,KAAK;YAAe;gBAClB,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO;gBAC9E,OAAO;oBACL,OAAO;oBACP,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;gBACpF;YACF;QAEA,KAAK;YAAmB;gBACtB,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACnC,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACjC;wBAAE,GAAG,IAAI;wBAAE,UAAU,KAAK,GAAG,CAAC,GAAG,OAAO,OAAO,CAAC,QAAQ;oBAAE,IAC1D,MACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG;gBAEjC,OAAO;oBACL,OAAO;oBACP,OAAO,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;gBACxF;YACF;QAEA,KAAK;YACH,OAAO;gBAAE,OAAO,EAAE;gBAAE,OAAO;YAAE;QAE/B,KAAK;YACH,OAAO;gBACL,OAAO,OAAO,OAAO;gBACrB,OAAO,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;YAC1F;QAEF;YACE,OAAO;IACX;AACF;AAUA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;QAAE,OAAO,EAAE;QAAE,OAAO;IAAE;IAExE,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,YAAY,KAAK,KAAK,CAAC;gBAC7B,SAAS;oBAAE,MAAM;oBAAa,SAAS;gBAAU;YACnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,MAAM,KAAK;IACzD,GAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,MAAM,UAAU,CAAC;QACf,SAAS;YAAE,MAAM;YAAY,SAAS;QAAQ;IAChD;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe,SAAS;QAAU;IACrD;IAEA,MAAM,iBAAiB,CAAC,WAAmB;QACzC,SAAS;YAAE,MAAM;YAAmB,SAAS;gBAAE,IAAI;gBAAW;YAAS;QAAE;IAC3E;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;IAChC;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAS;YAAY;YAAgB;QAAU;kBAClF;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [theme, setTheme] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n\n    // Check for saved theme preference or default to 'light'\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n      setTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const initialTheme = systemPrefersDark ? 'dark' : 'light';\n      setTheme(initialTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    // Apply theme to document\n    const root = document.documentElement;\n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n\n    // Save theme preference\n    localStorage.setItem('theme', theme);\n  }, [theme, mounted]);\n\n  const toggleTheme = () => {\n    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,yDAAyD;QACzD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,cAAc,CAAC,eAAe,WAAW,eAAe,MAAM,GAAG;YACnE,SAAS;QACX,OAAO;YACL,0BAA0B;YAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;YACnF,MAAM,eAAe,oBAAoB,SAAS;YAClD,SAAS;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,0BAA0B;QAC1B,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,UAAU,QAAQ;YACpB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;QAEA,wBAAwB;QACxB,aAAa,OAAO,CAAC,SAAS;IAChC,GAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,SAAS,CAAA,YAAa,cAAc,UAAU,SAAS;IACzD;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP;AAEO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}