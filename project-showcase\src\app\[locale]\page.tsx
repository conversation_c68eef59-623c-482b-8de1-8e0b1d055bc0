import Link from 'next/link';
import VisitorCounter from '@/components/VisitorCounter';

export default async function HomePage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  const features = [
    {
      icon: '💻',
      title: locale === 'ar' ? 'تطبيقات احترافية' : 'Professional Applications',
      description: locale === 'ar'
        ? 'تطبيقات مطورة بأحدث التقنيات وأفضل الممارسات'
        : 'Applications developed with latest technologies and best practices'
    },
    {
      icon: '🌐',
      title: locale === 'ar' ? 'مواقع متطورة' : 'Advanced Websites',
      description: locale === 'ar'
        ? 'مواقع ويب متجاوبة وسريعة مع تصميم عصري'
        : 'Responsive and fast websites with modern design'
    },
    {
      icon: '🛒',
      title: locale === 'ar' ? 'شراء آمن' : 'Secure Purchase',
      description: locale === 'ar'
        ? 'نظام دفع آمن مع دعم طرق دفع متعددة'
        : 'Secure payment system with multiple payment methods support'
    },
    {
      icon: '⭐',
      title: locale === 'ar' ? 'جودة عالية' : 'High Quality',
      description: locale === 'ar'
        ? 'جميع المشاريع مختبرة ومضمونة الجودة'
        : 'All projects are tested and quality guaranteed'
    }
  ];

  const stats = [
    {
      icon: '👥',
      value: '500+',
      label: locale === 'ar' ? 'عميل راضي' : 'Happy Clients'
    },
    {
      icon: '💻',
      value: '150+',
      label: locale === 'ar' ? 'مشروع مكتمل' : 'Completed Projects'
    },
    {
      icon: '📊',
      value: '99%',
      label: locale === 'ar' ? 'معدل الرضا' : 'Satisfaction Rate'
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                  Project Showcase
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                {/* Theme toggle will be added here */}
                🌙
              </button>
              <Link
                href={locale === 'en' ? '/ar' : '/en'}
                className="p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                {locale === 'ar' ? 'معرض المشاريع الاحترافية' : 'Professional Projects Showcase'}
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                {locale === 'ar' ? 'اكتشف تطبيقات ومواقع مذهلة للبيع' : 'Discover amazing applications and websites for sale'}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href={`/${locale}/projects`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-medium transition-colors"
                >
                  {locale === 'ar' ? 'تصفح المشاريع' : 'Browse Projects'}
                </Link>
                <Link
                  href={`/${locale}/about`}
                  className="border border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg text-lg font-medium transition-colors"
                >
                  {locale === 'ar' ? 'تعرف علينا' : 'Learn More'}
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {locale === 'ar' ? 'لماذا تختارنا؟' : 'Why Choose Us?'}
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {locale === 'ar'
                  ? 'نقدم أفضل الحلول التقنية مع ضمان الجودة والدعم المستمر'
                  : 'We provide the best technical solutions with quality guarantee and continuous support'
                }
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="text-center p-6 rounded-lg bg-gray-50 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">{feature.icon}</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-blue-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              {stats.map((stat, index) => (
                <div key={index} className="text-white">
                  <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">{stat.icon}</span>
                  </div>
                  <div className="text-4xl font-bold mb-2">{stat.value}</div>
                  <div className="text-xl opacity-90">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {locale === 'ar' ? 'ابدأ مشروعك اليوم' : 'Start Your Project Today'}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              {locale === 'ar'
                ? 'تصفح مجموعتنا الواسعة من المشاريع واختر ما يناسب احتياجاتك'
                : 'Browse our wide collection of projects and choose what fits your needs'
              }
            </p>
            <Link
              href={`/${locale}/projects`}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-medium transition-colors inline-flex items-center"
            >
              {locale === 'ar' ? 'المشاريع' : 'Projects'}
              <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </section>
      </main>

      <footer className="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="text-center md:text-left">
              <p className="text-gray-600 dark:text-gray-400">
                © 2024 Project Showcase. {locale === 'ar' ? 'جميع الحقوق محفوظة' : 'All rights reserved'}.
              </p>
            </div>
            <div className="flex justify-center md:justify-end">
              <VisitorCounter />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
