'use client';

import { useState, useEffect } from 'react';

interface VisitorStats {
  totalVisitors: number;
  todayVisitors: number;
  onlineVisitors: number;
}

export default function VisitorCounter() {
  const [stats, setStats] = useState<VisitorStats>({
    totalVisitors: 0,
    todayVisitors: 0,
    onlineVisitors: 0
  });

  useEffect(() => {
    // Simulate visitor tracking
    const trackVisitor = () => {
      // Get existing stats from localStorage
      const existingStats = localStorage.getItem('visitorStats');
      let currentStats: VisitorStats = {
        totalVisitors: 2847,
        todayVisitors: 156,
        onlineVisitors: 12
      };

      if (existingStats) {
        currentStats = JSON.parse(existingStats);
      }

      // Check if this is a new visitor today
      const lastVisit = localStorage.getItem('lastVisit');
      const today = new Date().toDateString();
      
      if (lastVisit !== today) {
        currentStats.todayVisitors += 1;
        currentStats.totalVisitors += 1;
        localStorage.setItem('lastVisit', today);
      }

      // Simulate online visitors (random number between 8-20)
      currentStats.onlineVisitors = Math.floor(Math.random() * 13) + 8;

      // Save updated stats
      localStorage.setItem('visitorStats', JSON.stringify(currentStats));
      setStats(currentStats);
    };

    trackVisitor();

    // Update online visitors every 30 seconds
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        onlineVisitors: Math.floor(Math.random() * 13) + 8
      }));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        إحصائيات الزوار
      </h3>
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-600 dark:text-gray-400">إجمالي الزوار:</span>
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            {stats.totalVisitors.toLocaleString()}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-600 dark:text-gray-400">زوار اليوم:</span>
          <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
            {stats.todayVisitors}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-600 dark:text-gray-400">متصل الآن:</span>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
            <span className="text-sm font-semibold text-green-600 dark:text-green-400">
              {stats.onlineVisitors}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
