'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

// Mock data - same as in projects page
const mockProjects = [
  {
    id: '1',
    title: 'E-commerce Mobile App',
    titleAr: 'تطبيق التجارة الإلكترونية',
    description: 'Modern e-commerce app with payment integration and user-friendly interface. Built with React Native for cross-platform compatibility.',
    descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع وواجهة سهلة الاستخدام. مبني بـ React Native للتوافق متعدد المنصات.',
    price: 299,
    category: 'app',
    image: '/api/placeholder/600/400',
    technologies: ['React Native', 'Node.js', 'MongoDB', 'Stripe', 'Firebase'],
    features: [
      'User Authentication & Registration',
      'Product Catalog with Search',
      'Shopping Cart & Wishlist',
      'Payment Gateway Integration',
      'Push Notifications',
      'Order Tracking',
      'User Reviews & Ratings',
      'Admin Dashboard'
    ],
    featuresAr: [
      'مصادقة وتسجيل المستخدم',
      'كتالوج المنتجات مع البحث',
      'سلة التسوق وقائمة الأمنيات',
      'تكامل بوابة الدفع',
      'الإشعارات الفورية',
      'تتبع الطلبات',
      'تقييمات ومراجعات المستخدمين',
      'لوحة تحكم الإدارة'
    ],
    demoUrl: '#',
    sourceCodeIncluded: true,
    documentation: true,
    support: '6 months',
    updates: '1 year'
  },
  // Add other projects here...
];

export default function ProjectDetailPage() {
  const params = useParams();
  const locale = params.locale as string;
  const projectId = params.id as string;
  
  // Find project by ID
  const project = mockProjects.find(p => p.id === projectId) || mockProjects[0];
  
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);

  const addToCart = () => {
    alert(locale === 'ar' ? 'تم إضافة المشروع إلى السلة!' : 'Project added to cart!');
  };

  const buyNow = () => {
    alert(locale === 'ar' ? 'سيتم توجيهك إلى صفحة الدفع' : 'Redirecting to checkout...');
  };

  // Mock images for gallery
  const projectImages = [
    '/api/placeholder/600/400',
    '/api/placeholder/600/400',
    '/api/placeholder/600/400',
    '/api/placeholder/600/400'
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  Project Showcase
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}/projects`}
                className="text-blue-600 hover:text-blue-700 transition-colors"
              >
                ← {locale === 'ar' ? 'العودة للمشاريع' : 'Back to Projects'}
              </Link>
              <Link
                href={locale === 'en' ? `/ar/projects/${projectId}` : `/en/projects/${projectId}`}
                className="p-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}
              </Link>
              <Link
                href={`/${locale}/cart`}
                className="relative p-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                🛒
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Project Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-4xl">
                📱 {project.category === 'app' ? 'App Preview' : 'Website Preview'}
              </span>
            </div>

            {/* Thumbnail Gallery */}
            <div className="grid grid-cols-4 gap-2">
              {projectImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square bg-gray-200 rounded-lg flex items-center justify-center text-xs ${
                    selectedImage === index ? 'ring-2 ring-blue-500' : ''
                  }`}
                >
                  {index + 1}
                </button>
              ))}
            </div>
          </div>

          {/* Project Details */}
          <div className="space-y-6">
            {/* Title and Category */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded">
                  {project.category === 'app' 
                    ? (locale === 'ar' ? 'تطبيق' : 'Application')
                    : (locale === 'ar' ? 'موقع ويب' : 'Website')
                  }
                </span>
                {project.sourceCodeIncluded && (
                  <span className="text-sm px-2 py-1 bg-green-100 text-green-800 rounded">
                    {locale === 'ar' ? 'يشمل الكود المصدري' : 'Source Code Included'}
                  </span>
                )}
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {locale === 'ar' ? project.titleAr : project.title}
              </h1>
              <p className="text-gray-600 text-lg">
                {locale === 'ar' ? project.descriptionAr : project.description}
              </p>
            </div>

            {/* Price */}
            <div className="border-t border-b border-gray-200 py-4">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                ${project.price}
              </div>
              <p className="text-sm text-gray-600">
                {locale === 'ar' ? 'سعر لمرة واحدة - ترخيص تجاري' : 'One-time price - Commercial license'}
              </p>
            </div>

            {/* Quantity and Actions */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <label className="text-sm font-medium text-gray-700">
                  {locale === 'ar' ? 'الكمية:' : 'Quantity:'}
                </label>
                <div className="flex items-center border border-gray-300 rounded">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="px-3 py-1 hover:bg-gray-100"
                  >
                    -
                  </button>
                  <span className="px-4 py-1 border-x border-gray-300">{quantity}</span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="px-3 py-1 hover:bg-gray-100"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={addToCart}
                  className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  {locale === 'ar' ? 'أضف إلى السلة' : 'Add to Cart'}
                </button>
                <button
                  onClick={buyNow}
                  className="flex-1 border border-blue-600 text-blue-600 py-3 px-6 rounded-lg hover:bg-blue-50 transition-colors font-medium"
                >
                  {locale === 'ar' ? 'اشتري الآن' : 'Buy Now'}
                </button>
              </div>

              {project.demoUrl && (
                <Link
                  href={project.demoUrl}
                  className="block w-full text-center border border-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {locale === 'ar' ? 'عرض تجريبي' : 'Live Demo'}
                </Link>
              )}
            </div>

            {/* What's Included */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">
                {locale === 'ar' ? 'ما يشمله المشروع:' : "What's Included:"}
              </h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  {locale === 'ar' ? 'الملفات المصدرية الكاملة' : 'Complete source files'}
                </li>
                {project.sourceCodeIncluded && (
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    {locale === 'ar' ? 'الكود المصدري' : 'Source code'}
                  </li>
                )}
                {project.documentation && (
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    {locale === 'ar' ? 'التوثيق والدليل' : 'Documentation & guide'}
                  </li>
                )}
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  {locale === 'ar' ? `دعم فني لمدة ${project.support}` : `${project.support} technical support`}
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  {locale === 'ar' ? `تحديثات مجانية لمدة ${project.updates}` : `${project.updates} free updates`}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Detailed Information Tabs */}
        <div className="mt-12">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button className="border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                {locale === 'ar' ? 'الميزات' : 'Features'}
              </button>
              <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                {locale === 'ar' ? 'التقنيات' : 'Technologies'}
              </button>
              <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                {locale === 'ar' ? 'المتطلبات' : 'Requirements'}
              </button>
            </nav>
          </div>

          <div className="py-6">
            {/* Features Tab */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {(locale === 'ar' ? project.featuresAr : project.features).map((feature, index) => (
                <div key={index} className="flex items-start">
                  <span className="text-blue-500 mr-2 mt-1">✓</span>
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Technologies Used */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {locale === 'ar' ? 'التقنيات المستخدمة:' : 'Technologies Used:'}
          </h3>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}
