globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/admin/projects/new/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/contexts/CartContext.tsx":{"*":{"id":"(ssr)/./src/contexts/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/ThemeContext.tsx":{"*":{"id":"(ssr)/./src/contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ThemeDemo.tsx":{"*":{"id":"(ssr)/./src/components/ThemeDemo.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ThemeToggle.tsx":{"*":{"id":"(ssr)/./src/components/ThemeToggle.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/VisitorCounter.tsx":{"*":{"id":"(ssr)/./src/components/VisitorCounter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/projects/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/projects/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/login/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/contact/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/cart/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/cart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/projects/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/admin/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/checkout/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/checkout/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/projects/new/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/admin/projects/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/projects/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/admin/projects/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\contexts\\CartContext.tsx":{"id":"(app-pages-browser)/./src/contexts/CartContext.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\contexts\\ThemeContext.tsx":{"id":"(app-pages-browser)/./src/contexts/ThemeContext.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\components\\ThemeDemo.tsx":{"id":"(app-pages-browser)/./src/components/ThemeDemo.tsx","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\components\\ThemeToggle.tsx":{"id":"(app-pages-browser)/./src/components/ThemeToggle.tsx","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\components\\VisitorCounter.tsx":{"id":"(app-pages-browser)/./src/components/VisitorCounter.tsx","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\projects\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/projects/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\projects\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/projects/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/admin/page.tsx","name":"*","chunks":["app/[locale]/admin/page","static/chunks/app/%5Blocale%5D/admin/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\contact\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/contact/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\cart\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/cart/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\admin\\projects\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/admin/projects/page.tsx","name":"*","chunks":["app/[locale]/admin/projects/page","static/chunks/app/%5Blocale%5D/admin/projects/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\checkout\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/checkout/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\admin\\projects\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/admin/projects/new/page.tsx","name":"*","chunks":["app/[locale]/admin/projects/new/page","static/chunks/app/%5Blocale%5D/admin/projects/new/page.js"],"async":false},"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\admin\\projects\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/admin/projects/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\":[],"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\layout":[{"inlined":false,"path":"static/css/app/[locale]/layout.css"}],"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\page":[],"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\admin\\page":[],"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\admin\\projects\\page":[],"C:\\Users\\<USER>\\Golden Eagle Company\\project-showcase\\src\\app\\[locale]\\admin\\projects\\new\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/CartContext.tsx":{"*":{"id":"(rsc)/./src/contexts/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/ThemeContext.tsx":{"*":{"id":"(rsc)/./src/contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ThemeDemo.tsx":{"*":{"id":"(rsc)/./src/components/ThemeDemo.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ThemeToggle.tsx":{"*":{"id":"(rsc)/./src/components/ThemeToggle.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/VisitorCounter.tsx":{"*":{"id":"(rsc)/./src/components/VisitorCounter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/projects/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/projects/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/login/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/contact/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/cart/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/cart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/projects/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/admin/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/checkout/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/checkout/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/projects/new/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/admin/projects/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/admin/projects/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/admin/projects/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}