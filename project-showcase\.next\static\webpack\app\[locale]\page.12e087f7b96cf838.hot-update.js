"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/ThemeDemo.tsx":
/*!**************************************!*\
  !*** ./src/components/ThemeDemo.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeDemo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _NoSSR__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NoSSR */ \"(app-pages-browser)/./src/components/NoSSR.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ThemeDemoContent() {\n    _s();\n    const { theme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                    children: \"Theme Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600 dark:text-gray-300\",\n                    children: [\n                        \"Current: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\",\n                            children: theme\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 20\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 text-xs text-gray-500 dark:text-gray-400\",\n                    children: theme === 'dark' ? '🌙 Dark mode active' : '☀️ Light mode active'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeDemoContent, \"JkSxfi8+JQlqgIgDOc3wQN+nVIw=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme\n    ];\n});\n_c = ThemeDemoContent;\nfunction ThemeDemo() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoSSR__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 bg-white border border-gray-200 rounded-lg p-4 shadow-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium text-gray-900 mb-2\",\n                        children: \"Theme Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Current: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono bg-gray-100 px-2 py-1 rounded\",\n                                children: \"light\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 53\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-gray-500\",\n                        children: \"☀️ Light mode active\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n                lineNumber: 31,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n            lineNumber: 30,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeDemoContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\components\\\\ThemeDemo.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ThemeDemo;\nvar _c, _c1;\n$RefreshReg$(_c, \"ThemeDemoContent\");\n$RefreshReg$(_c1, \"ThemeDemo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeDemo.tsx\n"));

/***/ })

});