"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/projects/[id]/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/[locale]/projects/[id]/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Mock data - same as in projects page\nconst mockProjects = [\n    {\n        id: '1',\n        title: 'E-commerce Mobile App',\n        titleAr: 'تطبيق التجارة الإلكترونية',\n        description: 'Modern e-commerce app with payment integration and user-friendly interface. Built with React Native for cross-platform compatibility.',\n        descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع وواجهة سهلة الاستخدام. مبني بـ React Native للتوافق متعدد المنصات.',\n        price: 299,\n        category: 'app',\n        image: '/api/placeholder/600/400',\n        technologies: [\n            'React Native',\n            'Node.js',\n            'MongoDB',\n            'Stripe',\n            'Firebase'\n        ],\n        features: [\n            'User Authentication & Registration',\n            'Product Catalog with Search',\n            'Shopping Cart & Wishlist',\n            'Payment Gateway Integration',\n            'Push Notifications',\n            'Order Tracking',\n            'User Reviews & Ratings',\n            'Admin Dashboard'\n        ],\n        featuresAr: [\n            'مصادقة وتسجيل المستخدم',\n            'كتالوج المنتجات مع البحث',\n            'سلة التسوق وقائمة الأمنيات',\n            'تكامل بوابة الدفع',\n            'الإشعارات الفورية',\n            'تتبع الطلبات',\n            'تقييمات ومراجعات المستخدمين',\n            'لوحة تحكم الإدارة'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true,\n        documentation: true,\n        support: '6 months',\n        updates: '1 year'\n    }\n];\nfunction ProjectDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const locale = params.locale;\n    const projectId = params.id;\n    // Find project by ID\n    const project = mockProjects.find((p)=>p.id === projectId) || mockProjects[0];\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const addToCart = ()=>{\n        alert(locale === 'ar' ? 'تم إضافة المشروع إلى السلة!' : 'Project added to cart!');\n    };\n    const buyNow = ()=>{\n        alert(locale === 'ar' ? 'سيتم توجيهك إلى صفحة الدفع' : 'Redirecting to checkout...');\n    };\n    // Mock images for gallery\n    const projectImages = [\n        '/api/placeholder/600/400',\n        '/api/placeholder/600/400',\n        '/api/placeholder/600/400',\n        '/api/placeholder/600/400'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\".concat(locale),\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83E\\uDD85\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-xl font-bold text-gray-900\",\n                                            children: locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/projects\"),\n                                        className: \"text-blue-600 hover:text-blue-700 transition-colors\",\n                                        children: [\n                                            \"← \",\n                                            locale === 'ar' ? 'العودة للمشاريع' : 'Back to Projects'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: locale === 'en' ? \"/ar/projects/\".concat(projectId) : \"/en/projects/\".concat(projectId),\n                                        className: \"p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/cart\"),\n                                        className: \"relative p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: \"\\uD83D\\uDED2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gray-200 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 text-4xl\",\n                                            children: [\n                                                \"\\uD83D\\uDCF1 \",\n                                                project.category === 'app' ? 'App Preview' : 'Website Preview'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2\",\n                                        children: projectImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedImage(index),\n                                                className: \"aspect-square bg-gray-200 rounded-lg flex items-center justify-center text-xs \".concat(selectedImage === index ? 'ring-2 ring-blue-500' : ''),\n                                                children: index + 1\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                        children: project.category === 'app' ? locale === 'ar' ? 'تطبيق' : 'Application' : locale === 'ar' ? 'موقع ويب' : 'Website'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    project.sourceCodeIncluded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: locale === 'ar' ? 'يشمل الكود المصدري' : 'Source Code Included'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: locale === 'ar' ? project.titleAr : project.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-lg\",\n                                                children: locale === 'ar' ? project.descriptionAr : project.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-b border-gray-200 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-blue-600 mb-2\",\n                                                children: [\n                                                    \"$\",\n                                                    project.price\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: locale === 'ar' ? 'سعر لمرة واحدة - ترخيص تجاري' : 'One-time price - Commercial license'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: locale === 'ar' ? 'الكمية:' : 'Quantity:'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center border border-gray-300 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                className: \"px-3 py-1 hover:bg-gray-100\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-4 py-1 border-x border-gray-300\",\n                                                                children: quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setQuantity(quantity + 1),\n                                                                className: \"px-3 py-1 hover:bg-gray-100\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: addToCart,\n                                                        className: \"flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                                        children: locale === 'ar' ? 'أضف إلى السلة' : 'Add to Cart'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: buyNow,\n                                                        className: \"flex-1 border border-blue-600 text-blue-600 py-3 px-6 rounded-lg hover:bg-blue-50 transition-colors font-medium\",\n                                                        children: locale === 'ar' ? 'اشتري الآن' : 'Buy Now'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            project.demoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: project.demoUrl,\n                                                className: \"block w-full text-center border border-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                children: locale === 'ar' ? 'عرض تجريبي' : 'Live Demo'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                children: locale === 'ar' ? 'ما يشمله المشروع:' : \"What's Included:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            locale === 'ar' ? 'الملفات المصدرية الكاملة' : 'Complete source files'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    project.sourceCodeIncluded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            locale === 'ar' ? 'الكود المصدري' : 'Source code'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    project.documentation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            locale === 'ar' ? 'التوثيق والدليل' : 'Documentation & guide'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            locale === 'ar' ? \"دعم فني لمدة \".concat(project.support) : \"\".concat(project.support, \" technical support\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            locale === 'ar' ? \"تحديثات مجانية لمدة \".concat(project.updates) : \"\".concat(project.updates, \" free updates\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600\",\n                                            children: locale === 'ar' ? 'الميزات' : 'Features'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700\",\n                                            children: locale === 'ar' ? 'التقنيات' : 'Technologies'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700\",\n                                            children: locale === 'ar' ? 'المتطلبات' : 'Requirements'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: (locale === 'ar' ? project.featuresAr : project.features).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-500 mr-2 mt-1\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: locale === 'ar' ? 'التقنيات المستخدمة:' : 'Technologies Used:'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: project.technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm\",\n                                        children: tech\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailPage, \"O/F6fK7NvKBoCADQHGO5RTSCQ5E=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = ProjectDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/projects/[id]/page.tsx\n"));

/***/ })

});