'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';

export default function NewProjectPage() {
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  
  const [formData, setFormData] = useState({
    title: '',
    titleAr: '',
    description: '',
    descriptionAr: '',
    price: '',
    category: 'app',
    technologies: '',
    features: '',
    featuresAr: '',
    demoUrl: '',
    sourceCodeIncluded: true,
    status: 'draft'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleImageUpload = (files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files).filter(file => {
      if (!file.type.startsWith('image/')) {
        alert(locale === 'ar' ? 'يرجى اختيار ملفات صور فقط' : 'Please select image files only');
        return false;
      }
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        alert(locale === 'ar' ? 'حجم الصورة يجب أن يكون أقل من 5 ميجابايت' : 'Image size should be less than 5MB');
        return false;
      }
      return true;
    });

    if (images.length + newFiles.length > 10) {
      alert(locale === 'ar' ? 'يمكن رفع 10 صور كحد أقصى' : 'Maximum 10 images allowed');
      return;
    }

    setImages(prev => [...prev, ...newFiles]);

    // Create previews
    newFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (images.length === 0) {
      alert(locale === 'ar' ? 'يرجى إضافة صورة واحدة على الأقل للمشروع' : 'Please add at least one image for the project');
      return;
    }

    setIsSubmitting(true);

    // Create FormData for file upload
    const submitData = new FormData();

    // Add form fields
    Object.entries(formData).forEach(([key, value]) => {
      submitData.append(key, value.toString());
    });

    // Add images
    images.forEach((image, index) => {
      submitData.append(`image_${index}`, image);
    });

    // Simulate API call with file upload
    setTimeout(() => {
      setIsSubmitting(false);
      alert(locale === 'ar' ? 'تم إضافة المشروع بنجاح مع الصور!' : 'Project added successfully with images!');
      router.push(`/${locale}/admin/projects`);
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/${locale}/admin`} className="flex items-center">
                <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">🦅</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  {locale === 'ar' ? 'إضافة مشروع جديد' : 'Add New Project'}
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}/admin/projects`}
                className="text-amber-600 hover:text-amber-700 transition-colors"
              >
                ← {locale === 'ar' ? 'العودة للمشاريع' : 'Back to Projects'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            {locale === 'ar' ? 'إضافة مشروع جديد' : 'Add New Project'}
          </h1>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'اسم المشروع (إنجليزي)' : 'Project Title (English)'}
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'اسم المشروع (عربي)' : 'Project Title (Arabic)'}
                </label>
                <input
                  type="text"
                  name="titleAr"
                  value={formData.titleAr}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Description */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'}
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
                </label>
                <textarea
                  name="descriptionAr"
                  value={formData.descriptionAr}
                  onChange={handleInputChange}
                  rows={4}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'ar' ? 'صور المشروع' : 'Project Images'}
                <span className="text-red-500 ml-1">*</span>
              </label>

              {/* Upload Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  dragActive
                    ? 'border-amber-500 bg-amber-50'
                    : 'border-gray-300 hover:border-amber-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <div className="space-y-4">
                  <div className="text-4xl text-gray-400">📷</div>
                  <div>
                    <p className="text-lg font-medium text-gray-700">
                      {locale === 'ar' ? 'اسحب الصور هنا أو انقر للاختيار' : 'Drag images here or click to select'}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {locale === 'ar'
                        ? 'PNG, JPG, GIF حتى 5 ميجابايت (10 صور كحد أقصى)'
                        : 'PNG, JPG, GIF up to 5MB (Maximum 10 images)'
                      }
                    </p>
                  </div>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 cursor-pointer transition-colors"
                  >
                    {locale === 'ar' ? 'اختيار الصور' : 'Choose Images'}
                  </label>
                </div>
              </div>

              {/* Image Previews */}
              {imagePreviews.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-700 mb-3">
                    {locale === 'ar' ? `الصور المحددة (${images.length})` : `Selected Images (${images.length})`}
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {imagePreviews.map((preview, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={preview}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                        >
                          ✕
                        </button>
                        <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                          {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    {locale === 'ar'
                      ? 'الصورة الأولى ستكون الصورة الرئيسية للمشروع'
                      : 'The first image will be the main project image'
                    }
                  </p>
                </div>
              )}
            </div>

            {/* Price and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'السعر ($)' : 'Price ($)'}
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'الفئة' : 'Category'}
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                >
                  <option value="app">{locale === 'ar' ? 'تطبيق' : 'Application'}</option>
                  <option value="website">{locale === 'ar' ? 'موقع ويب' : 'Website'}</option>
                </select>
              </div>
            </div>

            {/* Technologies */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'ar' ? 'التقنيات المستخدمة (مفصولة بفواصل)' : 'Technologies Used (comma separated)'}
              </label>
              <input
                type="text"
                name="technologies"
                value={formData.technologies}
                onChange={handleInputChange}
                placeholder="React, Node.js, MongoDB"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              />
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'الميزات (إنجليزي - كل ميزة في سطر)' : 'Features (English - one per line)'}
                </label>
                <textarea
                  name="features"
                  value={formData.features}
                  onChange={handleInputChange}
                  rows={6}
                  placeholder="User Authentication&#10;Payment Gateway&#10;Push Notifications"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'الميزات (عربي - كل ميزة في سطر)' : 'Features (Arabic - one per line)'}
                </label>
                <textarea
                  name="featuresAr"
                  value={formData.featuresAr}
                  onChange={handleInputChange}
                  rows={6}
                  placeholder="مصادقة المستخدم&#10;بوابة الدفع&#10;الإشعارات الفورية"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Demo URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'ar' ? 'رابط العرض التجريبي (اختياري)' : 'Demo URL (optional)'}
              </label>
              <input
                type="url"
                name="demoUrl"
                value={formData.demoUrl}
                onChange={handleInputChange}
                placeholder="https://demo.example.com"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              />
            </div>

            {/* Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="sourceCodeIncluded"
                  checked={formData.sourceCodeIncluded}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                />
                <label className="ml-2 text-sm text-gray-700">
                  {locale === 'ar' ? 'يشمل الكود المصدري' : 'Source code included'}
                </label>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'حالة المشروع' : 'Project Status'}
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                >
                  <option value="draft">{locale === 'ar' ? 'مسودة' : 'Draft'}</option>
                  <option value="active">{locale === 'ar' ? 'نشط' : 'Active'}</option>
                </select>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex space-x-4 pt-6">
              <button
                type="submit"
                disabled={isSubmitting || images.length === 0}
                className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                {isSubmitting
                  ? (locale === 'ar' ? 'جاري الحفظ والرفع...' : 'Saving and Uploading...')
                  : (locale === 'ar' ? 'حفظ المشروع' : 'Save Project')
                }
              </button>
              {images.length === 0 && (
                <p className="text-sm text-red-600 mt-2">
                  {locale === 'ar' ? 'يرجى إضافة صورة واحدة على الأقل' : 'Please add at least one image'}
                </p>
              )}
              <Link
                href={`/${locale}/admin/projects`}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors"
              >
                {locale === 'ar' ? 'إلغاء' : 'Cancel'}
              </Link>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}
