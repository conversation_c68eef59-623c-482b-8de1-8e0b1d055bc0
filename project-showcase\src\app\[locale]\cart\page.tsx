'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';

// Mock cart data
const mockCartItems = [
  {
    id: '1',
    project: {
      id: '1',
      title: 'E-commerce Mobile App',
      titleAr: 'تطبيق التجارة الإلكترونية',
      price: 299,
      category: 'app' as const,
      image: '/api/placeholder/100/100'
    },
    quantity: 1
  },
  {
    id: '2',
    project: {
      id: '2',
      title: 'Restaurant Website',
      titleAr: 'موقع مطعم',
      price: 199,
      category: 'website' as const,
      image: '/api/placeholder/100/100'
    },
    quantity: 2
  }
];

export default function CartPage() {
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  const { state, updateQuantity, removeItem, clearCart } = useCart();

  const handleUpdateQuantity = (projectId: string, newQuantity: number) => {
    updateQuantity(projectId, newQuantity);
  };

  const handleRemoveItem = (projectId: string) => {
    removeItem(projectId);
  };

  const handleClearCart = () => {
    clearCart();
  };

  const subtotal = state.total;
  const tax = subtotal * 0.1; // 10% tax
  const total = subtotal + tax;

  const proceedToCheckout = () => {
    router.push(`/${locale}/checkout`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">🦅</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  {locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'}
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}/projects`}
                className="text-blue-600 hover:text-blue-700 transition-colors"
              >
                {locale === 'ar' ? 'متابعة التسوق' : 'Continue Shopping'}
              </Link>
              <Link
                href={locale === 'en' ? '/ar/cart' : '/en/cart'}
                className="p-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {locale === 'ar' ? 'سلة المشتريات' : 'Shopping Cart'}
          </h1>
          <p className="text-gray-600 mt-2">
            {locale === 'ar'
              ? `${state.items.length} عنصر في سلتك`
              : `${state.items.length} items in your cart`
            }
          </p>
        </div>

        {state.items.length === 0 ? (
          /* Empty Cart */
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🛒</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {locale === 'ar' ? 'سلتك فارغة' : 'Your cart is empty'}
            </h3>
            <p className="text-gray-600 mb-6">
              {locale === 'ar' 
                ? 'ابدأ بإضافة بعض المشاريع الرائعة إلى سلتك'
                : 'Start by adding some amazing projects to your cart'
              }
            </p>
            <Link
              href={`/${locale}/projects`}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {locale === 'ar' ? 'تصفح المشاريع' : 'Browse Projects'}
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              {state.items.map((item) => (
                <div key={item.project.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center space-x-4">
                    {/* Project Image */}
                    <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-gray-500">
                        {item.project.category === 'app' ? '📱' : '🌐'}
                      </span>
                    </div>

                    {/* Project Details */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900">
                        {locale === 'ar' ? item.project.titleAr : item.project.title}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {item.project.category === 'app' 
                          ? (locale === 'ar' ? 'تطبيق' : 'Application')
                          : (locale === 'ar' ? 'موقع ويب' : 'Website')
                        }
                      </p>
                      <p className="text-lg font-semibold text-blue-600 mt-1">
                        ${item.project.price}
                      </p>
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleUpdateQuantity(item.project.id, item.quantity - 1)}
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                      >
                        -
                      </button>
                      <span className="w-8 text-center font-medium">{item.quantity}</span>
                      <button
                        onClick={() => handleUpdateQuantity(item.project.id, item.quantity + 1)}
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                      >
                        +
                      </button>
                    </div>

                    {/* Item Total */}
                    <div className="text-right">
                      <p className="text-lg font-semibold text-gray-900">
                        ${item.project.price * item.quantity}
                      </p>
                      <button
                        onClick={() => handleRemoveItem(item.project.id)}
                        className="text-sm text-red-600 hover:text-red-700 mt-1"
                      >
                        {locale === 'ar' ? 'إزالة' : 'Remove'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {/* Clear Cart Button */}
              <div className="flex justify-end">
                <button
                  onClick={handleClearCart}
                  className="text-sm text-gray-600 hover:text-red-600 transition-colors"
                >
                  {locale === 'ar' ? 'إفراغ السلة' : 'Clear Cart'}
                </button>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {locale === 'ar' ? 'ملخص الطلب' : 'Order Summary'}
                </h3>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">
                      {locale === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}
                    </span>
                    <span className="font-medium">${subtotal.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">
                      {locale === 'ar' ? 'الضريبة (10%)' : 'Tax (10%)'}
                    </span>
                    <span className="font-medium">${tax.toFixed(2)}</span>
                  </div>

                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between">
                      <span className="text-lg font-semibold text-gray-900">
                        {locale === 'ar' ? 'الإجمالي' : 'Total'}
                      </span>
                      <span className="text-lg font-semibold text-gray-900">
                        ${total.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={proceedToCheckout}
                  className="w-full mt-6 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  {locale === 'ar' ? 'متابعة للدفع' : 'Proceed to Checkout'}
                </button>

                <div className="mt-4 text-center">
                  <Link
                    href={`/${locale}/projects`}
                    className="text-sm text-blue-600 hover:text-blue-700"
                  >
                    {locale === 'ar' ? 'متابعة التسوق' : 'Continue Shopping'}
                  </Link>
                </div>

                {/* Security Notice */}
                <div className="mt-6 p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="mr-2">🔒</span>
                    <span>
                      {locale === 'ar' 
                        ? 'دفع آمن ومشفر'
                        : 'Secure & encrypted payment'
                      }
                    </span>
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="mt-4">
                  <p className="text-sm text-gray-600 mb-2">
                    {locale === 'ar' ? 'طرق الدفع المقبولة:' : 'Accepted payment methods:'}
                  </p>
                  <div className="flex space-x-2">
                    <div className="w-8 h-6 bg-blue-600 rounded text-white text-xs flex items-center justify-center">
                      VISA
                    </div>
                    <div className="w-8 h-6 bg-red-600 rounded text-white text-xs flex items-center justify-center">
                      MC
                    </div>
                    <div className="w-8 h-6 bg-green-600 rounded text-white text-xs flex items-center justify-center">
                      ZC
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
