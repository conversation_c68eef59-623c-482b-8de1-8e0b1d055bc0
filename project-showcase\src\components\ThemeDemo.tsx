'use client';

import { useTheme } from '@/contexts/ThemeContext';
import NoSSR from './NoSSR';

function ThemeDemoContent() {
  const { theme } = useTheme();

  return (
    <div className="fixed bottom-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg">
      <div className="text-sm">
        <div className="font-medium text-gray-900 dark:text-white mb-2">
          Theme Status
        </div>
        <div className="text-gray-600 dark:text-gray-300">
          Current: <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">{theme}</span>
        </div>
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          {theme === 'dark' ? '🌙 Dark mode active' : '☀️ Light mode active'}
        </div>
      </div>
    </div>
  );
}

export default function ThemeDemo() {
  return (
    <NoSSR
      fallback={
        <div className="fixed bottom-4 left-4 bg-white border border-gray-200 rounded-lg p-4 shadow-lg">
          <div className="text-sm">
            <div className="font-medium text-gray-900 mb-2">Theme Status</div>
            <div className="text-gray-600">Current: <span className="font-mono bg-gray-100 px-2 py-1 rounded">light</span></div>
            <div className="mt-2 text-xs text-gray-500">☀️ Light mode active</div>
          </div>
        </div>
      }
    >
      <ThemeDemoContent />
    </NoSSR>
  );
}
