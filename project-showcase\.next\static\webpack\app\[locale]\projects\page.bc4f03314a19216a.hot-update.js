"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/projects/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/projects/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/projects/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./src/contexts/CartContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Mock data for projects\nconst mockProjects = [\n    {\n        id: '1',\n        title: 'E-commerce Mobile App',\n        titleAr: 'تطبيق التجارة الإلكترونية',\n        description: 'Modern e-commerce app with payment integration',\n        descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع',\n        price: 299,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'React Native',\n            'Node.js',\n            'MongoDB'\n        ],\n        features: [\n            'User Authentication',\n            'Payment Gateway',\n            'Push Notifications'\n        ],\n        featuresAr: [\n            'مصادقة المستخدم',\n            'بوابة الدفع',\n            'الإشعارات الفورية'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '2',\n        title: 'Restaurant Website',\n        titleAr: 'موقع مطعم',\n        description: 'Responsive restaurant website with online ordering',\n        descriptionAr: 'موقع مطعم متجاوب مع نظام الطلب عبر الإنترنت',\n        price: 199,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Next.js',\n            'Tailwind CSS',\n            'Stripe'\n        ],\n        features: [\n            'Online Menu',\n            'Order Management',\n            'Table Booking'\n        ],\n        featuresAr: [\n            'قائمة طعام إلكترونية',\n            'إدارة الطلبات',\n            'حجز الطاولات'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '3',\n        title: 'Task Management App',\n        titleAr: 'تطبيق إدارة المهام',\n        description: 'Productivity app for team collaboration',\n        descriptionAr: 'تطبيق إنتاجية للتعاون الجماعي',\n        price: 249,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'React',\n            'Firebase',\n            'Material-UI'\n        ],\n        features: [\n            'Team Collaboration',\n            'Real-time Updates',\n            'File Sharing'\n        ],\n        featuresAr: [\n            'التعاون الجماعي',\n            'التحديثات الفورية',\n            'مشاركة الملفات'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: false\n    },\n    {\n        id: '4',\n        title: 'Portfolio Website',\n        titleAr: 'موقع معرض أعمال',\n        description: 'Creative portfolio website for designers',\n        descriptionAr: 'موقع معرض أعمال إبداعي للمصممين',\n        price: 149,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Vue.js',\n            'SCSS',\n            'Netlify'\n        ],\n        features: [\n            'Gallery',\n            'Contact Form',\n            'Blog'\n        ],\n        featuresAr: [\n            'معرض الصور',\n            'نموذج الاتصال',\n            'المدونة'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '5',\n        title: 'Learning Management System',\n        titleAr: 'نظام إدارة التعلم',\n        description: 'Complete LMS with video streaming',\n        descriptionAr: 'نظام إدارة تعلم كامل مع بث الفيديو',\n        price: 499,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Laravel',\n            'Vue.js',\n            'MySQL'\n        ],\n        features: [\n            'Video Streaming',\n            'Quiz System',\n            'Progress Tracking'\n        ],\n        featuresAr: [\n            'بث الفيديو',\n            'نظام الاختبارات',\n            'تتبع التقدم'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '6',\n        title: 'Fitness Tracker App',\n        titleAr: 'تطبيق تتبع اللياقة',\n        description: 'Health and fitness tracking mobile app',\n        descriptionAr: 'تطبيق محمول لتتبع الصحة واللياقة',\n        price: 199,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Flutter',\n            'Firebase',\n            'HealthKit'\n        ],\n        features: [\n            'Workout Tracking',\n            'Nutrition Log',\n            'Social Features'\n        ],\n        featuresAr: [\n            'تتبع التمارين',\n            'سجل التغذية',\n            'الميزات الاجتماعية'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: false\n    }\n];\nfunction ProjectsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const locale = params.locale;\n    const { addItem } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockProjects);\n    const [filteredProjects, setFilteredProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockProjects);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    // Filter and search logic\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            let filtered = projects;\n            // Filter by category\n            if (selectedCategory !== 'all') {\n                filtered = filtered.filter({\n                    \"ProjectsPage.useEffect\": (project)=>project.category === selectedCategory\n                }[\"ProjectsPage.useEffect\"]);\n            }\n            // Filter by search query\n            if (searchQuery) {\n                filtered = filtered.filter({\n                    \"ProjectsPage.useEffect\": (project)=>{\n                        const title = locale === 'ar' ? project.titleAr : project.title;\n                        const description = locale === 'ar' ? project.descriptionAr : project.description;\n                        return title.toLowerCase().includes(searchQuery.toLowerCase()) || description.toLowerCase().includes(searchQuery.toLowerCase());\n                    }\n                }[\"ProjectsPage.useEffect\"]);\n            }\n            // Sort projects\n            filtered = [\n                ...filtered\n            ].sort({\n                \"ProjectsPage.useEffect\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price-low':\n                            return a.price - b.price;\n                        case 'price-high':\n                            return b.price - a.price;\n                        case 'name':\n                            const titleA = locale === 'ar' ? a.titleAr : a.title;\n                            const titleB = locale === 'ar' ? b.titleAr : b.title;\n                            return titleA.localeCompare(titleB);\n                        default:\n                            return 0;\n                    }\n                }\n            }[\"ProjectsPage.useEffect\"]);\n            setFilteredProjects(filtered);\n        }\n    }[\"ProjectsPage.useEffect\"], [\n        selectedCategory,\n        searchQuery,\n        sortBy,\n        projects,\n        locale\n    ]);\n    const addToCart = (project)=>{\n        // This will be implemented with the cart context later\n        alert(locale === 'ar' ? 'تم إضافة المشروع إلى السلة!' : 'Project added to cart!');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\".concat(locale),\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83E\\uDD85\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-xl font-bold text-gray-900\",\n                                            children: locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: locale === 'en' ? '/ar/projects' : '/en/projects',\n                                        className: \"p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/cart\"),\n                                        className: \"relative p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: \"\\uD83D\\uDED2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: locale === 'ar' ? 'مشاريعنا' : 'Our Projects'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: locale === 'ar' ? 'اكتشف مجموعتنا الواسعة من التطبيقات والمواقع عالية الجودة' : 'Discover our wide collection of high-quality applications and websites'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: locale === 'ar' ? 'البحث في المشاريع...' : 'Search projects...',\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 right-3 flex items-center pointer-events-none\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: locale === 'ar' ? 'الفئة:' : 'Category:'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                className: \"px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: locale === 'ar' ? 'الكل' : 'All'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"app\",\n                                                        children: locale === 'ar' ? 'التطبيقات' : 'Applications'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"website\",\n                                                        children: locale === 'ar' ? 'المواقع' : 'Websites'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: locale === 'ar' ? 'ترتيب حسب:' : 'Sort by:'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"newest\",\n                                                        children: locale === 'ar' ? 'الأحدث' : 'Newest'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"price-low\",\n                                                        children: locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"price-high\",\n                                                        children: locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: locale === 'ar' ? 'الاسم' : 'Name'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: locale === 'ar' ? \"\".concat(filteredProjects.length, \" مشروع\") : \"\".concat(filteredProjects.length, \" projects\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gray-200 rounded-t-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: [\n                                                \"\\uD83D\\uDCF1 \",\n                                                project.category === 'app' ? 'App' : 'Website'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: locale === 'ar' ? project.titleAr : project.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                        children: project.category === 'app' ? locale === 'ar' ? 'تطبيق' : 'App' : locale === 'ar' ? 'موقع' : 'Website'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm mb-4\",\n                                                children: locale === 'ar' ? project.descriptionAr : project.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        project.technologies.slice(0, 3).map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded\",\n                                                                children: tech\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        project.technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded\",\n                                                            children: [\n                                                                \"+\",\n                                                                project.technologies.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                        children: locale === 'ar' ? 'الميزات:' : 'Features:'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-xs text-gray-600 space-y-1\",\n                                                        children: (locale === 'ar' ? project.featuresAr : project.features).slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    feature\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            project.price\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/\".concat(locale, \"/projects/\").concat(project.id),\n                                                                className: \"px-3 py-1 text-sm border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition-colors\",\n                                                                children: locale === 'ar' ? 'التفاصيل' : 'Details'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>addToCart(project),\n                                                                className: \"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                                                                children: locale === 'ar' ? 'أضف للسلة' : 'Add to Cart'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, project.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    filteredProjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: locale === 'ar' ? 'لم يتم العثور على مشاريع' : 'No projects found'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: locale === 'ar' ? 'جرب تغيير معايير البحث أو الفلترة' : 'Try changing your search or filter criteria'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsPage, \"BMgo0xkYtSwDgaNAOXZSr4GfRdM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams,\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart\n    ];\n});\n_c = ProjectsPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/projects/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst cartReducer = (state, action)=>{\n    switch(action.type){\n        case 'ADD_ITEM':\n            {\n                const existingItem = state.items.find((item)=>item.project.id === action.payload.id);\n                if (existingItem) {\n                    const updatedItems = state.items.map((item)=>item.project.id === action.payload.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                    return {\n                        items: updatedItems,\n                        total: updatedItems.reduce((sum, item)=>sum + item.project.price * item.quantity, 0)\n                    };\n                }\n                const newItems = [\n                    ...state.items,\n                    {\n                        project: action.payload,\n                        quantity: 1\n                    }\n                ];\n                return {\n                    items: newItems,\n                    total: newItems.reduce((sum, item)=>sum + item.project.price * item.quantity, 0)\n                };\n            }\n        case 'REMOVE_ITEM':\n            {\n                const newItems = state.items.filter((item)=>item.project.id !== action.payload);\n                return {\n                    items: newItems,\n                    total: newItems.reduce((sum, item)=>sum + item.project.price * item.quantity, 0)\n                };\n            }\n        case 'UPDATE_QUANTITY':\n            {\n                const updatedItems = state.items.map((item)=>item.project.id === action.payload.id ? {\n                        ...item,\n                        quantity: Math.max(0, action.payload.quantity)\n                    } : item).filter((item)=>item.quantity > 0);\n                return {\n                    items: updatedItems,\n                    total: updatedItems.reduce((sum, item)=>sum + item.project.price * item.quantity, 0)\n                };\n            }\n        case 'CLEAR_CART':\n            return {\n                items: [],\n                total: 0\n            };\n        case 'LOAD_CART':\n            return {\n                items: action.payload,\n                total: action.payload.reduce((sum, item)=>sum + item.project.price * item.quantity, 0)\n            };\n        default:\n            return state;\n    }\n};\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst CartProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(cartReducer, {\n        items: [],\n        total: 0\n    });\n    // Load cart from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            const savedCart = localStorage.getItem('cart');\n            if (savedCart) {\n                try {\n                    const cartItems = JSON.parse(savedCart);\n                    dispatch({\n                        type: 'LOAD_CART',\n                        payload: cartItems\n                    });\n                } catch (error) {\n                    console.error('Error loading cart from localStorage:', error);\n                }\n            }\n        }\n    }[\"CartProvider.useEffect\"], []);\n    // Save cart to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            localStorage.setItem('cart', JSON.stringify(state.items));\n        }\n    }[\"CartProvider.useEffect\"], [\n        state.items\n    ]);\n    const addItem = (project)=>{\n        dispatch({\n            type: 'ADD_ITEM',\n            payload: project\n        });\n    };\n    const removeItem = (projectId)=>{\n        dispatch({\n            type: 'REMOVE_ITEM',\n            payload: projectId\n        });\n    };\n    const updateQuantity = (projectId, quantity)=>{\n        dispatch({\n            type: 'UPDATE_QUANTITY',\n            payload: {\n                id: projectId,\n                quantity\n            }\n        });\n    };\n    const clearCart = ()=>{\n        dispatch({\n            type: 'CLEAR_CART'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            state,\n            addItem,\n            removeItem,\n            updateQuantity,\n            clearCart\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\contexts\\\\CartContext.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CartProvider, \"NyO79qdj8A98++kurZehvYJy5Lk=\");\n_c = CartProvider;\nconst useCart = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n};\n_s1(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/CartContext.tsx\n"));

/***/ })

});