'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';

// Mock project data
const mockProject = {
  id: '1',
  title: 'E-commerce Mobile App',
  titleAr: 'تطبيق التجارة الإلكترونية',
  description: 'Modern e-commerce app with payment integration',
  descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع',
  price: 299,
  category: 'app',
  technologies: 'React Native, Node.js, MongoDB',
  features: 'User Authentication\nPayment Gateway\nPush Notifications\nProduct Catalog',
  featuresAr: 'مصادقة المستخدم\nبوابة الدفع\nالإشعارات الفورية\nكتالوج المنتجات',
  demoUrl: 'https://demo.example.com',
  sourceCodeIncluded: true,
  status: 'active',
  images: [
    '/api/placeholder/400/300',
    '/api/placeholder/400/300',
    '/api/placeholder/400/300'
  ]
};

export default function EditProjectPage() {
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  const projectId = params.id as string;
  
  const [formData, setFormData] = useState({
    title: '',
    titleAr: '',
    description: '',
    descriptionAr: '',
    price: '',
    category: 'app',
    technologies: '',
    features: '',
    featuresAr: '',
    demoUrl: '',
    sourceCodeIncluded: true,
    status: 'draft'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingImages, setExistingImages] = useState<string[]>([]);
  const [newImages, setNewImages] = useState<File[]>([]);
  const [newImagePreviews, setNewImagePreviews] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);

  useEffect(() => {
    // Load project data
    const project = mockProject;
    setFormData({
      title: project.title,
      titleAr: project.titleAr,
      description: project.description,
      descriptionAr: project.descriptionAr,
      price: project.price.toString(),
      category: project.category,
      technologies: project.technologies,
      features: project.features,
      featuresAr: project.featuresAr,
      demoUrl: project.demoUrl,
      sourceCodeIncluded: project.sourceCodeIncluded,
      status: project.status
    });
    setExistingImages(project.images);
  }, [projectId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleNewImageUpload = (files: FileList | null) => {
    if (!files) return;
    
    const newFiles = Array.from(files).filter(file => {
      if (!file.type.startsWith('image/')) {
        alert(locale === 'ar' ? 'يرجى اختيار ملفات صور فقط' : 'Please select image files only');
        return false;
      }
      if (file.size > 5 * 1024 * 1024) {
        alert(locale === 'ar' ? 'حجم الصورة يجب أن يكون أقل من 5 ميجابايت' : 'Image size should be less than 5MB');
        return false;
      }
      return true;
    });

    if (existingImages.length + newImages.length + newFiles.length > 10) {
      alert(locale === 'ar' ? 'يمكن رفع 10 صور كحد أقصى' : 'Maximum 10 images allowed');
      return;
    }

    setNewImages(prev => [...prev, ...newFiles]);

    newFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeExistingImage = (index: number) => {
    setExistingImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeNewImage = (index: number) => {
    setNewImages(prev => prev.filter((_, i) => i !== index));
    setNewImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleNewImageUpload(e.dataTransfer.files);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (existingImages.length === 0 && newImages.length === 0) {
      alert(locale === 'ar' ? 'يرجى الاحتفاظ بصورة واحدة على الأقل للمشروع' : 'Please keep at least one image for the project');
      return;
    }

    setIsSubmitting(true);

    const submitData = new FormData();
    
    Object.entries(formData).forEach(([key, value]) => {
      submitData.append(key, value.toString());
    });
    
    submitData.append('existingImages', JSON.stringify(existingImages));
    
    newImages.forEach((image, index) => {
      submitData.append(`newImage_${index}`, image);
    });

    setTimeout(() => {
      setIsSubmitting(false);
      alert(locale === 'ar' ? 'تم تحديث المشروع بنجاح!' : 'Project updated successfully!');
      router.push(`/${locale}/admin/projects`);
    }, 3000);
  };

  const totalImages = existingImages.length + newImages.length;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/${locale}/admin`} className="flex items-center">
                <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">🦅</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  {locale === 'ar' ? 'تعديل المشروع' : 'Edit Project'}
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}/admin/projects`}
                className="text-amber-600 hover:text-amber-700 transition-colors"
              >
                ← {locale === 'ar' ? 'العودة للمشاريع' : 'Back to Projects'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            {locale === 'ar' ? 'تعديل المشروع' : 'Edit Project'}
          </h1>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'اسم المشروع (إنجليزي)' : 'Project Title (English)'}
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'اسم المشروع (عربي)' : 'Project Title (Arabic)'}
                </label>
                <input
                  type="text"
                  name="titleAr"
                  value={formData.titleAr}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Description */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'}
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
                </label>
                <textarea
                  name="descriptionAr"
                  value={formData.descriptionAr}
                  onChange={handleInputChange}
                  rows={4}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Image Management */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'ar' ? 'صور المشروع' : 'Project Images'}
                <span className="text-gray-500 ml-1">({totalImages}/10)</span>
              </label>
              
              {/* Existing Images */}
              {existingImages.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-600 mb-2">
                    {locale === 'ar' ? 'الصور الحالية' : 'Current Images'}
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {existingImages.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={image}
                          alt={`Current ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={() => removeExistingImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                        >
                          ✕
                        </button>
                        <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                          {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* New Images Upload */}
              {totalImages < 10 && (
                <div
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    dragActive 
                      ? 'border-amber-500 bg-amber-50' 
                      : 'border-gray-300 hover:border-amber-400'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <div className="space-y-4">
                    <div className="text-4xl text-gray-400">📷</div>
                    <div>
                      <p className="text-lg font-medium text-gray-700">
                        {locale === 'ar' ? 'إضافة صور جديدة' : 'Add New Images'}
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        {locale === 'ar' 
                          ? `يمكن إضافة ${10 - totalImages} صور إضافية`
                          : `Can add ${10 - totalImages} more images`
                        }
                      </p>
                    </div>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => handleNewImageUpload(e.target.files)}
                      className="hidden"
                      id="new-image-upload"
                    />
                    <label
                      htmlFor="new-image-upload"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 cursor-pointer transition-colors"
                    >
                      {locale === 'ar' ? 'اختيار صور جديدة' : 'Choose New Images'}
                    </label>
                  </div>
                </div>
              )}

              {/* New Image Previews */}
              {newImagePreviews.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-600 mb-2">
                    {locale === 'ar' ? 'الصور الجديدة' : 'New Images'}
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {newImagePreviews.map((preview, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={preview}
                          alt={`New ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg border border-amber-200"
                        />
                        <button
                          type="button"
                          onClick={() => removeNewImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                        >
                          ✕
                        </button>
                        <div className="absolute bottom-1 left-1 bg-amber-600 text-white text-xs px-1 rounded">
                          {locale === 'ar' ? 'جديد' : 'New'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex space-x-4 pt-6">
              <button
                type="submit"
                disabled={isSubmitting || totalImages === 0}
                className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                {isSubmitting 
                  ? (locale === 'ar' ? 'جاري التحديث...' : 'Updating...')
                  : (locale === 'ar' ? 'تحديث المشروع' : 'Update Project')
                }
              </button>
              <Link
                href={`/${locale}/admin/projects`}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors"
              >
                {locale === 'ar' ? 'إلغاء' : 'Cancel'}
              </Link>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}
