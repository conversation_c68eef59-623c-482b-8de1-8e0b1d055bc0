'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface StatItem {
  icon: string;
  value: number;
  label: string;
  color: string;
  bgColor: string;
}

export default function VisitorStats() {
  const [mounted, setMounted] = useState(false);
  const [stats, setStats] = useState<StatItem[]>([
    {
      icon: '👥',
      value: 0,
      label: 'إجمالي الزوار',
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30'
    },
    {
      icon: '🌍',
      value: 0,
      label: 'زوار اليوم',
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100 dark:bg-green-900/30'
    },
    {
      icon: '📊',
      value: 0,
      label: 'المشاهدات',
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30'
    },
    {
      icon: '⭐',
      value: 0,
      label: 'التقييمات',
      color: 'text-amber-600 dark:text-amber-400',
      bgColor: 'bg-amber-100 dark:bg-amber-900/30'
    }
  ]);

  useEffect(() => {
    setMounted(true);
    
    // محاكاة جلب البيانات من API
    const fetchStats = () => {
      const newStats = [
        {
          icon: '👥',
          value: Math.floor(Math.random() * 10000) + 5000,
          label: 'إجمالي الزوار',
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-100 dark:bg-blue-900/30'
        },
        {
          icon: '🌍',
          value: Math.floor(Math.random() * 500) + 100,
          label: 'زوار اليوم',
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-100 dark:bg-green-900/30'
        },
        {
          icon: '📊',
          value: Math.floor(Math.random() * 50000) + 20000,
          label: 'المشاهدات',
          color: 'text-purple-600 dark:text-purple-400',
          bgColor: 'bg-purple-100 dark:bg-purple-900/30'
        },
        {
          icon: '⭐',
          value: Math.floor(Math.random() * 100) + 50,
          label: 'التقييمات',
          color: 'text-amber-600 dark:text-amber-400',
          bgColor: 'bg-amber-100 dark:bg-amber-900/30'
        }
      ];
      setStats(newStats);
    };

    fetchStats();
    
    // تحديث الإحصائيات كل 30 ثانية
    const interval = setInterval(fetchStats, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const animateValue = (start: number, end: number, duration: number = 2000) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const timer = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(start + (end - start) * progress);
        
        if (progress >= 1) {
          clearInterval(timer);
          resolve(end);
        }
      }, 16);
    });
  };

  if (!mounted) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            📈 إحصائيات الموقع
          </h3>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="text-center p-3 rounded-lg bg-gray-100 dark:bg-gray-700 animate-pulse">
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto mb-2"></div>
              <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded mb-1"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          📈 إحصائيات الموقع
        </h3>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-500 dark:text-gray-400">مباشر</span>
        </div>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`text-center p-4 rounded-lg ${stat.bgColor} hover:scale-105 transition-all duration-300 cursor-pointer group relative overflow-hidden`}
          >
            {/* Background Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div className="relative z-10">
              <div className="text-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
                {stat.icon}
              </div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                className={`text-2xl font-bold ${stat.color} mb-1`}
              >
                {stat.value.toLocaleString()}
              </motion.div>
              <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                {stat.label}
              </div>

              {/* Mini Progress Bar */}
              <div className="mt-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min((stat.value / 15000) * 100, 100)}%` }}
                  transition={{ duration: 2, delay: 1 + index * 0.1 }}
                  className={`h-1 rounded-full ${stat.color.replace('text-', 'bg-')}`}
                ></motion.div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Weekly Chart */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          📈 إحصائيات الأسبوع
        </h4>
        <div className="flex items-end justify-between h-16 gap-1">
          {['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'].map((day, index) => {
            const height = Math.random() * 60 + 20;
            return (
              <div key={day} className="flex flex-col items-center flex-1">
                <motion.div
                  initial={{ height: 0 }}
                  animate={{ height: `${height}%` }}
                  transition={{ duration: 1, delay: 2 + index * 0.1 }}
                  className="w-full bg-gradient-to-t from-amber-500 to-amber-300 rounded-t-sm min-h-[4px] mb-1"
                  title={`${day}: ${Math.floor(height * 10)} زائر`}
                ></motion.div>
                <span className="text-xs text-gray-500 dark:text-gray-400 transform rotate-45 origin-bottom-left">
                  {day.slice(0, 2)}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}</span>
          <span className="flex items-center gap-1">
            <div className="w-1 h-1 bg-green-500 rounded-full animate-ping"></div>
            متصل
          </span>
        </div>
      </div>
    </motion.div>
  );
}
