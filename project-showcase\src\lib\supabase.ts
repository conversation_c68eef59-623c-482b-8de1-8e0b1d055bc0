import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Project {
  id: string;
  title: string;
  description: string;
  price: number;
  category: 'app' | 'website';
  images: string[];
  features: string[];
  technologies: string[];
  demo_url?: string;
  source_code_included: boolean;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'customer';
  created_at: string;
}

export interface Order {
  id: string;
  user_id: string;
  total_amount: number;
  status: 'pending' | 'completed' | 'cancelled';
  payment_method: 'mastercard' | 'visa' | 'zain_cash';
  created_at: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  project_id: string;
  price: number;
  created_at: string;
}

export interface CartItem {
  project: Project;
  quantity: number;
}
