'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function CheckoutPage() {
  const params = useParams();
  const locale = params.locale as string;
  
  const [paymentMethod, setPaymentMethod] = useState('visa');
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    country: 'Iraq',
    // Card details
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
    // Zain Cash
    zainCashNumber: ''
  });

  const [isProcessing, setIsProcessing] = useState(false);

  // Mock cart total
  const orderTotal = 498.00;
  const tax = 49.80;
  const total = orderTotal + tax;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      alert(locale === 'ar' ? 'تم الدفع بنجاح!' : 'Payment successful!');
    }, 3000);
  };

  const paymentMethods = [
    {
      id: 'visa',
      name: 'Visa',
      nameAr: 'فيزا',
      icon: '💳',
      color: 'bg-blue-600'
    },
    {
      id: 'mastercard',
      name: 'Mastercard',
      nameAr: 'ماستر كارد',
      icon: '💳',
      color: 'bg-red-600'
    },
    {
      id: 'zain_cash',
      name: 'Zain Cash Iraq',
      nameAr: 'زين كاش العراق',
      icon: '📱',
      color: 'bg-green-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  Project Showcase
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}/cart`}
                className="text-blue-600 hover:text-blue-700 transition-colors"
              >
                ← {locale === 'ar' ? 'العودة للسلة' : 'Back to Cart'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {locale === 'ar' ? 'إتمام الطلب' : 'Checkout'}
          </h1>
          <p className="text-gray-600 mt-2">
            {locale === 'ar' 
              ? 'أكمل معلوماتك لإتمام عملية الشراء'
              : 'Complete your information to finish your purchase'
            }
          </p>
        </div>

        <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {locale === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'ar' ? 'الاسم الأول' : 'First Name'}
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'ar' ? 'الاسم الأخير' : 'Last Name'}
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {locale === 'ar' ? 'طريقة الدفع' : 'Payment Method'}
              </h3>
              
              {/* Payment Method Selection */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {paymentMethods.map((method) => (
                  <label
                    key={method.id}
                    className={`relative flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      paymentMethod === method.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value={method.id}
                      checked={paymentMethod === method.id}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="sr-only"
                    />
                    <div className="flex items-center">
                      <div className={`w-8 h-8 ${method.color} rounded flex items-center justify-center text-white mr-3`}>
                        {method.icon}
                      </div>
                      <span className="font-medium text-gray-900">
                        {locale === 'ar' ? method.nameAr : method.name}
                      </span>
                    </div>
                    {paymentMethod === method.id && (
                      <div className="absolute top-2 right-2 text-blue-500">
                        ✓
                      </div>
                    )}
                  </label>
                ))}
              </div>

              {/* Payment Details */}
              {(paymentMethod === 'visa' || paymentMethod === 'mastercard') && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {locale === 'ar' ? 'رقم البطاقة' : 'Card Number'}
                    </label>
                    <input
                      type="text"
                      name="cardNumber"
                      value={formData.cardNumber}
                      onChange={handleInputChange}
                      placeholder="1234 5678 9012 3456"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {locale === 'ar' ? 'تاريخ الانتهاء' : 'Expiry Date'}
                      </label>
                      <input
                        type="text"
                        name="expiryDate"
                        value={formData.expiryDate}
                        onChange={handleInputChange}
                        placeholder="MM/YY"
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {locale === 'ar' ? 'رمز الأمان' : 'CVV'}
                      </label>
                      <input
                        type="text"
                        name="cvv"
                        value={formData.cvv}
                        onChange={handleInputChange}
                        placeholder="123"
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {locale === 'ar' ? 'اسم حامل البطاقة' : 'Cardholder Name'}
                    </label>
                    <input
                      type="text"
                      name="cardName"
                      value={formData.cardName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}

              {paymentMethod === 'zain_cash' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'ar' ? 'رقم زين كاش' : 'Zain Cash Number'}
                  </label>
                  <input
                    type="tel"
                    name="zainCashNumber"
                    value={formData.zainCashNumber}
                    onChange={handleInputChange}
                    placeholder="07xxxxxxxxx"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-sm text-gray-600 mt-2">
                    {locale === 'ar' 
                      ? 'ستتلقى رسالة نصية لتأكيد الدفع'
                      : 'You will receive an SMS to confirm payment'
                    }
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {locale === 'ar' ? 'ملخص الطلب' : 'Order Summary'}
              </h3>

              {/* Order Items */}
              <div className="space-y-3 mb-4">
                <div className="flex justify-between text-sm">
                  <span>{locale === 'ar' ? 'تطبيق التجارة الإلكترونية' : 'E-commerce Mobile App'}</span>
                  <span>$299</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{locale === 'ar' ? 'موقع مطعم (×2)' : 'Restaurant Website (×2)'}</span>
                  <span>$398</span>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    {locale === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}
                  </span>
                  <span>${orderTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    {locale === 'ar' ? 'الضريبة' : 'Tax'}
                  </span>
                  <span>${tax.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold pt-2 border-t border-gray-200">
                  <span>{locale === 'ar' ? 'الإجمالي' : 'Total'}</span>
                  <span>${total.toFixed(2)}</span>
                </div>
              </div>

              <button
                type="submit"
                disabled={isProcessing}
                className="w-full mt-6 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing 
                  ? (locale === 'ar' ? 'جاري المعالجة...' : 'Processing...')
                  : (locale === 'ar' ? `ادفع $${total.toFixed(2)}` : `Pay $${total.toFixed(2)}`)
                }
              </button>

              <div className="mt-4 text-center text-sm text-gray-600">
                <div className="flex items-center justify-center mb-2">
                  <span className="mr-1">🔒</span>
                  {locale === 'ar' ? 'دفع آمن ومشفر' : 'Secure & encrypted payment'}
                </div>
                <p>
                  {locale === 'ar' 
                    ? 'معلوماتك محمية بتشفير SSL'
                    : 'Your information is protected with SSL encryption'
                  }
                </p>
              </div>
            </div>
          </div>
        </form>
      </main>
    </div>
  );
}
