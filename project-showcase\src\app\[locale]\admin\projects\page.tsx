'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

// Mock data for projects
const mockProjects = [
  {
    id: '1',
    title: 'E-commerce Mobile App',
    titleAr: 'تطبيق التجارة الإلكترونية',
    description: 'Modern e-commerce app with payment integration',
    descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع',
    price: 299,
    category: 'app',
    status: 'active',
    sales: 15,
    createdAt: '2024-01-15',
    technologies: ['React Native', 'Node.js', 'MongoDB']
  },
  {
    id: '2',
    title: 'Restaurant Website',
    titleAr: 'موقع مطعم',
    description: 'Responsive restaurant website with online ordering',
    descriptionAr: 'موقع مطعم متجاوب مع نظام الطلب عبر الإنترنت',
    price: 199,
    category: 'website',
    status: 'active',
    sales: 8,
    createdAt: '2024-01-10',
    technologies: ['Next.js', 'Tailwind CSS', 'Stripe']
  },
  {
    id: '3',
    title: 'Task Management App',
    titleAr: 'تطبيق إدارة المهام',
    description: 'Productivity app for team collaboration',
    descriptionAr: 'تطبيق إنتاجية للتعاون الجماعي',
    price: 249,
    category: 'app',
    status: 'draft',
    sales: 0,
    createdAt: '2024-01-05',
    technologies: ['React', 'Firebase', 'Material-UI']
  },
  {
    id: '4',
    title: 'Portfolio Website',
    titleAr: 'موقع معرض أعمال',
    description: 'Creative portfolio website for designers',
    descriptionAr: 'موقع معرض أعمال إبداعي للمصممين',
    price: 149,
    category: 'website',
    status: 'active',
    sales: 22,
    createdAt: '2024-01-01',
    technologies: ['Vue.js', 'SCSS', 'Netlify']
  }
];

export default function AdminProjectsPage() {
  const params = useParams();
  const locale = params.locale as string;
  const [projects, setProjects] = useState(mockProjects);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  const handleSelectProject = (projectId: string) => {
    setSelectedProjects(prev => 
      prev.includes(projectId) 
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProjects.length === projects.length) {
      setSelectedProjects([]);
    } else {
      setSelectedProjects(projects.map(p => p.id));
    }
  };

  const handleDeleteProject = (projectId: string) => {
    setProjectToDelete(projectId);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (projectToDelete) {
      setProjects(prev => prev.filter(p => p.id !== projectToDelete));
      setSelectedProjects(prev => prev.filter(id => id !== projectToDelete));
      setProjectToDelete(null);
      setShowDeleteModal(false);
    }
  };

  const handleBulkDelete = () => {
    setProjects(prev => prev.filter(p => !selectedProjects.includes(p.id)));
    setSelectedProjects([]);
  };

  const toggleProjectStatus = (projectId: string) => {
    setProjects(prev => prev.map(p => 
      p.id === projectId 
        ? { ...p, status: p.status === 'active' ? 'draft' : 'active' }
        : p
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/${locale}/admin`} className="flex items-center">
                <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">🦅</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  {locale === 'ar' ? 'إدارة المشاريع' : 'Manage Projects'}
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}/admin`}
                className="text-amber-600 hover:text-amber-700 transition-colors"
              >
                ← {locale === 'ar' ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {locale === 'ar' ? 'إدارة المشاريع' : 'Manage Projects'}
            </h1>
            <p className="text-gray-600 mt-2">
              {locale === 'ar' 
                ? 'إدارة وتحرير المشاريع المعروضة في الموقع'
                : 'Manage and edit projects displayed on the website'
              }
            </p>
          </div>
          <Link
            href={`/${locale}/admin/projects/new`}
            className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            {locale === 'ar' ? '+ إضافة مشروع جديد' : '+ Add New Project'}
          </Link>
        </div>

        {/* Bulk Actions */}
        {selectedProjects.length > 0 && (
          <div className="mb-6 bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-amber-800">
                {locale === 'ar' 
                  ? `تم تحديد ${selectedProjects.length} مشروع`
                  : `${selectedProjects.length} projects selected`
                }
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={handleBulkDelete}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm transition-colors"
                >
                  {locale === 'ar' ? 'حذف المحدد' : 'Delete Selected'}
                </button>
                <button
                  onClick={() => setSelectedProjects([])}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm transition-colors"
                >
                  {locale === 'ar' ? 'إلغاء التحديد' : 'Clear Selection'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Projects Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedProjects.length === projects.length}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'المشروع' : 'Project'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'الفئة' : 'Category'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'السعر' : 'Price'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'المبيعات' : 'Sales'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {projects.map((project) => (
                  <tr key={project.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedProjects.includes(project.id)}
                        onChange={() => handleSelectProject(project.id)}
                        className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                          <span className="text-gray-500">
                            {project.category === 'app' ? '📱' : '🌐'}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {locale === 'ar' ? project.titleAr : project.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            {locale === 'ar' ? project.descriptionAr : project.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        project.category === 'app' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {project.category === 'app' 
                          ? (locale === 'ar' ? 'تطبيق' : 'App')
                          : (locale === 'ar' ? 'موقع' : 'Website')
                        }
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${project.price}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {project.sales}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => toggleProjectStatus(project.id)}
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          project.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {project.status === 'active' 
                          ? (locale === 'ar' ? 'نشط' : 'Active')
                          : (locale === 'ar' ? 'مسودة' : 'Draft')
                        }
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link
                          href={`/${locale}/admin/projects/${project.id}/edit`}
                          className="text-amber-600 hover:text-amber-700"
                        >
                          {locale === 'ar' ? 'تعديل' : 'Edit'}
                        </Link>
                        <Link
                          href={`/${locale}/projects/${project.id}`}
                          className="text-blue-600 hover:text-blue-700"
                          target="_blank"
                        >
                          {locale === 'ar' ? 'عرض' : 'View'}
                        </Link>
                        <button
                          onClick={() => handleDeleteProject(project.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          {locale === 'ar' ? 'حذف' : 'Delete'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Empty State */}
        {projects.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📂</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {locale === 'ar' ? 'لا توجد مشاريع' : 'No projects found'}
            </h3>
            <p className="text-gray-600 mb-6">
              {locale === 'ar' 
                ? 'ابدأ بإضافة مشروعك الأول'
                : 'Get started by adding your first project'
              }
            </p>
            <Link
              href={`/${locale}/admin/projects/new`}
              className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              {locale === 'ar' ? 'إضافة مشروع جديد' : 'Add New Project'}
            </Link>
          </div>
        )}
      </main>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {locale === 'ar' ? 'تأكيد الحذف' : 'Confirm Delete'}
            </h3>
            <p className="text-gray-600 mb-6">
              {locale === 'ar' 
                ? 'هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.'
                : 'Are you sure you want to delete this project? This action cannot be undone.'
              }
            </p>
            <div className="flex space-x-4">
              <button
                onClick={confirmDelete}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                {locale === 'ar' ? 'حذف' : 'Delete'}
              </button>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md transition-colors"
              >
                {locale === 'ar' ? 'إلغاء' : 'Cancel'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
