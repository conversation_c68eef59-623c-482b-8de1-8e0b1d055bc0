"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/projects/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/projects/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/projects/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Mock data for projects\nconst mockProjects = [\n    {\n        id: '1',\n        title: 'E-commerce Mobile App',\n        titleAr: 'تطبيق التجارة الإلكترونية',\n        description: 'Modern e-commerce app with payment integration',\n        descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع',\n        price: 299,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'React Native',\n            'Node.js',\n            'MongoDB'\n        ],\n        features: [\n            'User Authentication',\n            'Payment Gateway',\n            'Push Notifications'\n        ],\n        featuresAr: [\n            'مصادقة المستخدم',\n            'بوابة الدفع',\n            'الإشعارات الفورية'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '2',\n        title: 'Restaurant Website',\n        titleAr: 'موقع مطعم',\n        description: 'Responsive restaurant website with online ordering',\n        descriptionAr: 'موقع مطعم متجاوب مع نظام الطلب عبر الإنترنت',\n        price: 199,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Next.js',\n            'Tailwind CSS',\n            'Stripe'\n        ],\n        features: [\n            'Online Menu',\n            'Order Management',\n            'Table Booking'\n        ],\n        featuresAr: [\n            'قائمة طعام إلكترونية',\n            'إدارة الطلبات',\n            'حجز الطاولات'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '3',\n        title: 'Task Management App',\n        titleAr: 'تطبيق إدارة المهام',\n        description: 'Productivity app for team collaboration',\n        descriptionAr: 'تطبيق إنتاجية للتعاون الجماعي',\n        price: 249,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'React',\n            'Firebase',\n            'Material-UI'\n        ],\n        features: [\n            'Team Collaboration',\n            'Real-time Updates',\n            'File Sharing'\n        ],\n        featuresAr: [\n            'التعاون الجماعي',\n            'التحديثات الفورية',\n            'مشاركة الملفات'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: false\n    },\n    {\n        id: '4',\n        title: 'Portfolio Website',\n        titleAr: 'موقع معرض أعمال',\n        description: 'Creative portfolio website for designers',\n        descriptionAr: 'موقع معرض أعمال إبداعي للمصممين',\n        price: 149,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Vue.js',\n            'SCSS',\n            'Netlify'\n        ],\n        features: [\n            'Gallery',\n            'Contact Form',\n            'Blog'\n        ],\n        featuresAr: [\n            'معرض الصور',\n            'نموذج الاتصال',\n            'المدونة'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '5',\n        title: 'Learning Management System',\n        titleAr: 'نظام إدارة التعلم',\n        description: 'Complete LMS with video streaming',\n        descriptionAr: 'نظام إدارة تعلم كامل مع بث الفيديو',\n        price: 499,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Laravel',\n            'Vue.js',\n            'MySQL'\n        ],\n        features: [\n            'Video Streaming',\n            'Quiz System',\n            'Progress Tracking'\n        ],\n        featuresAr: [\n            'بث الفيديو',\n            'نظام الاختبارات',\n            'تتبع التقدم'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '6',\n        title: 'Fitness Tracker App',\n        titleAr: 'تطبيق تتبع اللياقة',\n        description: 'Health and fitness tracking mobile app',\n        descriptionAr: 'تطبيق محمول لتتبع الصحة واللياقة',\n        price: 199,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Flutter',\n            'Firebase',\n            'HealthKit'\n        ],\n        features: [\n            'Workout Tracking',\n            'Nutrition Log',\n            'Social Features'\n        ],\n        featuresAr: [\n            'تتبع التمارين',\n            'سجل التغذية',\n            'الميزات الاجتماعية'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: false\n    }\n];\nfunction ProjectsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const locale = params.locale;\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockProjects);\n    const [filteredProjects, setFilteredProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockProjects);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    // Filter and search logic\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            let filtered = projects;\n            // Filter by category\n            if (selectedCategory !== 'all') {\n                filtered = filtered.filter({\n                    \"ProjectsPage.useEffect\": (project)=>project.category === selectedCategory\n                }[\"ProjectsPage.useEffect\"]);\n            }\n            // Filter by search query\n            if (searchQuery) {\n                filtered = filtered.filter({\n                    \"ProjectsPage.useEffect\": (project)=>{\n                        const title = locale === 'ar' ? project.titleAr : project.title;\n                        const description = locale === 'ar' ? project.descriptionAr : project.description;\n                        return title.toLowerCase().includes(searchQuery.toLowerCase()) || description.toLowerCase().includes(searchQuery.toLowerCase());\n                    }\n                }[\"ProjectsPage.useEffect\"]);\n            }\n            // Sort projects\n            filtered = [\n                ...filtered\n            ].sort({\n                \"ProjectsPage.useEffect\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price-low':\n                            return a.price - b.price;\n                        case 'price-high':\n                            return b.price - a.price;\n                        case 'name':\n                            const titleA = locale === 'ar' ? a.titleAr : a.title;\n                            const titleB = locale === 'ar' ? b.titleAr : b.title;\n                            return titleA.localeCompare(titleB);\n                        default:\n                            return 0;\n                    }\n                }\n            }[\"ProjectsPage.useEffect\"]);\n            setFilteredProjects(filtered);\n        }\n    }[\"ProjectsPage.useEffect\"], [\n        selectedCategory,\n        searchQuery,\n        sortBy,\n        projects,\n        locale\n    ]);\n    const addToCart = (project)=>{\n        // This will be implemented with the cart context later\n        alert(locale === 'ar' ? 'تم إضافة المشروع إلى السلة!' : 'Project added to cart!');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\".concat(locale),\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83E\\uDD85\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-xl font-bold text-gray-900\",\n                                            children: locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: locale === 'en' ? '/ar/projects' : '/en/projects',\n                                        className: \"p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/cart\"),\n                                        className: \"relative p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: \"\\uD83D\\uDED2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: locale === 'ar' ? 'مشاريعنا' : 'Our Projects'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: locale === 'ar' ? 'اكتشف مجموعتنا الواسعة من التطبيقات والمواقع عالية الجودة' : 'Discover our wide collection of high-quality applications and websites'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: locale === 'ar' ? 'البحث في المشاريع...' : 'Search projects...',\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 right-3 flex items-center pointer-events-none\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: locale === 'ar' ? 'الفئة:' : 'Category:'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                className: \"px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: locale === 'ar' ? 'الكل' : 'All'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"app\",\n                                                        children: locale === 'ar' ? 'التطبيقات' : 'Applications'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"website\",\n                                                        children: locale === 'ar' ? 'المواقع' : 'Websites'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: locale === 'ar' ? 'ترتيب حسب:' : 'Sort by:'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"newest\",\n                                                        children: locale === 'ar' ? 'الأحدث' : 'Newest'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"price-low\",\n                                                        children: locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"price-high\",\n                                                        children: locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: locale === 'ar' ? 'الاسم' : 'Name'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: locale === 'ar' ? \"\".concat(filteredProjects.length, \" مشروع\") : \"\".concat(filteredProjects.length, \" projects\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gray-200 rounded-t-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: [\n                                                \"\\uD83D\\uDCF1 \",\n                                                project.category === 'app' ? 'App' : 'Website'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: locale === 'ar' ? project.titleAr : project.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                        children: project.category === 'app' ? locale === 'ar' ? 'تطبيق' : 'App' : locale === 'ar' ? 'موقع' : 'Website'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm mb-4\",\n                                                children: locale === 'ar' ? project.descriptionAr : project.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        project.technologies.slice(0, 3).map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded\",\n                                                                children: tech\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        project.technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded\",\n                                                            children: [\n                                                                \"+\",\n                                                                project.technologies.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                        children: locale === 'ar' ? 'الميزات:' : 'Features:'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-xs text-gray-600 space-y-1\",\n                                                        children: (locale === 'ar' ? project.featuresAr : project.features).slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    feature\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            project.price\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/\".concat(locale, \"/projects/\").concat(project.id),\n                                                                className: \"px-3 py-1 text-sm border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition-colors\",\n                                                                children: locale === 'ar' ? 'التفاصيل' : 'Details'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>addToCart(project),\n                                                                className: \"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                                                                children: locale === 'ar' ? 'أضف للسلة' : 'Add to Cart'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, project.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    filteredProjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: locale === 'ar' ? 'لم يتم العثور على مشاريع' : 'No projects found'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: locale === 'ar' ? 'جرب تغيير معايير البحث أو الفلترة' : 'Try changing your search or filter criteria'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsPage, \"i1ciYQmdBxhtlWI8KyYkHLkQ/5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = ProjectsPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/projects/page.tsx\n"));

/***/ })

});