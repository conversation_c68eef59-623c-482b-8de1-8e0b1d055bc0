'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { useState, useEffect } from 'react';

interface ThemeToggleProps {
  className?: string;
}

export default function ThemeToggle({ className = '' }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className={`flex items-center ${className}`}>
        <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-300">
          <div className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-1">
            <span className="flex h-full w-full items-center justify-center text-xs">☀️</span>
          </div>
        </div>
        <span className="ml-2 text-sm text-gray-600 hidden sm:block">فاتح</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      {/* Toggle Switch */}
      <button
        onClick={toggleTheme}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${
          theme === 'dark' ? 'bg-amber-600' : 'bg-gray-300'
        }`}
        title={theme === 'light' ? 'تفعيل الوضع الداكن' : 'تفعيل الوضع الفاتح'}
        aria-label={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
      >
        <span className="sr-only">
          {theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
        </span>

        {/* Toggle Circle */}
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 shadow-sm ${
            theme === 'dark' ? 'translate-x-6' : 'translate-x-1'
          }`}
        >
          {/* Icon inside the circle */}
          <span className="flex h-full w-full items-center justify-center text-xs">
            {theme === 'light' ? '☀️' : '🌙'}
          </span>
        </span>
      </button>

      {/* Optional Label */}
      <span className="ml-2 text-sm text-gray-600 dark:text-gray-400 hidden sm:block">
        {theme === 'light' ? 'فاتح' : 'داكن'}
      </span>
    </div>
  );
}
