'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

// Mock data for dashboard
const mockStats = {
  totalProjects: 24,
  totalSales: 15420,
  totalOrders: 89,
  totalVisitors: 2847,
  monthlyRevenue: [
    { month: 'Jan', revenue: 2400, orders: 12 },
    { month: 'Feb', revenue: 1398, orders: 8 },
    { month: 'Mar', revenue: 9800, orders: 24 },
    { month: 'Apr', revenue: 3908, orders: 18 },
    { month: 'May', revenue: 4800, orders: 22 },
    { month: 'Jun', revenue: 3800, orders: 16 }
  ],
  categoryStats: [
    { category: 'Apps', count: 14, revenue: 8420 },
    { category: 'Websites', count: 10, revenue: 7000 }
  ],
  recentOrders: [
    {
      id: '1',
      customer: 'أحمد محمد',
      project: 'E-commerce App',
      amount: 299,
      status: 'completed',
      date: '2024-01-15'
    },
    {
      id: '2',
      customer: '<PERSON>',
      project: 'Restaurant Website',
      amount: 199,
      status: 'pending',
      date: '2024-01-14'
    },
    {
      id: '3',
      customer: 'علي حسن',
      project: 'Task Management App',
      amount: 249,
      status: 'completed',
      date: '2024-01-13'
    }
  ]
};

export default function AdminDashboard() {
  const params = useParams();
  const locale = params.locale as string;
  const [selectedPeriod, setSelectedPeriod] = useState('6months');

  // Simple chart component (since we can't use recharts easily)
  const SimpleBarChart = ({ data, title }: { data: any[], title: string }) => (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center">
            <div className="w-16 text-sm text-gray-600">{item.month}</div>
            <div className="flex-1 mx-4">
              <div className="bg-gray-200 rounded-full h-4">
                <div
                  className="bg-blue-600 h-4 rounded-full"
                  style={{ width: `${(item.revenue / 10000) * 100}%` }}
                ></div>
              </div>
            </div>
            <div className="w-20 text-sm font-medium text-gray-900">${item.revenue}</div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  Admin Dashboard
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}`}
                className="text-blue-600 hover:text-blue-700 transition-colors"
              >
                {locale === 'ar' ? 'عرض الموقع' : 'View Site'}
              </Link>
              <Link
                href={locale === 'en' ? '/ar/admin' : '/en/admin'}
                className="p-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}
              </Link>
              <button className="text-gray-700 hover:text-red-600 transition-colors">
                {locale === 'ar' ? 'تسجيل خروج' : 'Logout'}
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {locale === 'ar' ? 'لوحة التحكم' : 'Dashboard'}
          </h1>
          <p className="text-gray-600 mt-2">
            {locale === 'ar' 
              ? 'نظرة عامة على أداء موقعك ومبيعاتك'
              : 'Overview of your site performance and sales'
            }
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {locale === 'ar' ? 'إجمالي المشاريع' : 'Total Projects'}
                </p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.totalProjects}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {locale === 'ar' ? 'إجمالي المبيعات' : 'Total Sales'}
                </p>
                <p className="text-2xl font-bold text-gray-900">${mockStats.totalSales}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <span className="text-2xl">🛒</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {locale === 'ar' ? 'إجمالي الطلبات' : 'Total Orders'}
                </p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.totalOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <span className="text-2xl">👥</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {locale === 'ar' ? 'إجمالي الزوار' : 'Total Visitors'}
                </p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.totalVisitors}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Revenue Chart */}
          <SimpleBarChart 
            data={mockStats.monthlyRevenue} 
            title={locale === 'ar' ? 'الإيرادات الشهرية' : 'Monthly Revenue'} 
          />

          {/* Category Stats */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {locale === 'ar' ? 'إحصائيات الفئات' : 'Category Statistics'}
            </h3>
            <div className="space-y-4">
              {mockStats.categoryStats.map((category, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {locale === 'ar' 
                        ? (category.category === 'Apps' ? 'التطبيقات' : 'المواقع')
                        : category.category
                      }
                    </h4>
                    <p className="text-sm text-gray-600">
                      {category.count} {locale === 'ar' ? 'مشروع' : 'projects'}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-gray-900">${category.revenue}</p>
                    <p className="text-sm text-gray-600">
                      {locale === 'ar' ? 'إيرادات' : 'revenue'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                {locale === 'ar' ? 'الطلبات الأخيرة' : 'Recent Orders'}
              </h3>
              <Link
                href={`/${locale}/admin/orders`}
                className="text-blue-600 hover:text-blue-700 text-sm"
              >
                {locale === 'ar' ? 'عرض الكل' : 'View All'}
              </Link>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'العميل' : 'Customer'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'المشروع' : 'Project'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'المبلغ' : 'Amount'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {locale === 'ar' ? 'التاريخ' : 'Date'}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockStats.recentOrders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {order.customer}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {order.project}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${order.amount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        order.status === 'completed' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {order.status === 'completed' 
                          ? (locale === 'ar' ? 'مكتمل' : 'Completed')
                          : (locale === 'ar' ? 'معلق' : 'Pending')
                        }
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {order.date}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Link
            href={`/${locale}/admin/projects/new`}
            className="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition-colors text-center"
          >
            <div className="text-3xl mb-2">➕</div>
            <h3 className="text-lg font-semibold">
              {locale === 'ar' ? 'إضافة مشروع جديد' : 'Add New Project'}
            </h3>
          </Link>

          <Link
            href={`/${locale}/admin/orders`}
            className="bg-green-600 text-white p-6 rounded-lg hover:bg-green-700 transition-colors text-center"
          >
            <div className="text-3xl mb-2">📋</div>
            <h3 className="text-lg font-semibold">
              {locale === 'ar' ? 'إدارة الطلبات' : 'Manage Orders'}
            </h3>
          </Link>

          <Link
            href={`/${locale}/admin/analytics`}
            className="bg-purple-600 text-white p-6 rounded-lg hover:bg-purple-700 transition-colors text-center"
          >
            <div className="text-3xl mb-2">📊</div>
            <h3 className="text-lg font-semibold">
              {locale === 'ar' ? 'التحليلات المتقدمة' : 'Advanced Analytics'}
            </h3>
          </Link>
        </div>
      </main>
    </div>
  );
}
