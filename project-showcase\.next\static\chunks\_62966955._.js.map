{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\n// Types for cart functionality\ninterface Project {\n  id: string;\n  title: string;\n  titleAr: string;\n  price: number;\n  category: 'app' | 'website';\n  image: string;\n}\n\ninterface CartItem {\n  project: Project;\n  quantity: number;\n}\n\ninterface CartState {\n  items: CartItem[];\n  total: number;\n}\n\ntype CartAction =\n  | { type: 'ADD_ITEM'; payload: Project }\n  | { type: 'REMOVE_ITEM'; payload: string }\n  | { type: 'UPDATE_QUANTITY'; payload: { id: string; quantity: number } }\n  | { type: 'CLEAR_CART' }\n  | { type: 'LOAD_CART'; payload: CartItem[] };\n\nconst cartReducer = (state: CartState, action: CartAction): CartState => {\n  switch (action.type) {\n    case 'ADD_ITEM': {\n      const existingItem = state.items.find(item => item.project.id === action.payload.id);\n      \n      if (existingItem) {\n        const updatedItems = state.items.map(item =>\n          item.project.id === action.payload.id\n            ? { ...item, quantity: item.quantity + 1 }\n            : item\n        );\n        return {\n          items: updatedItems,\n          total: updatedItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n        };\n      }\n      \n      const newItems = [...state.items, { project: action.payload, quantity: 1 }];\n      return {\n        items: newItems,\n        total: newItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    }\n    \n    case 'REMOVE_ITEM': {\n      const newItems = state.items.filter(item => item.project.id !== action.payload);\n      return {\n        items: newItems,\n        total: newItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    }\n    \n    case 'UPDATE_QUANTITY': {\n      const updatedItems = state.items.map(item =>\n        item.project.id === action.payload.id\n          ? { ...item, quantity: Math.max(0, action.payload.quantity) }\n          : item\n      ).filter(item => item.quantity > 0);\n      \n      return {\n        items: updatedItems,\n        total: updatedItems.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    }\n    \n    case 'CLEAR_CART':\n      return { items: [], total: 0 };\n    \n    case 'LOAD_CART':\n      return {\n        items: action.payload,\n        total: action.payload.reduce((sum, item) => sum + (item.project.price * item.quantity), 0)\n      };\n    \n    default:\n      return state;\n  }\n};\n\ninterface CartContextType {\n  state: CartState;\n  addItem: (project: Project) => void;\n  removeItem: (projectId: string) => void;\n  updateQuantity: (projectId: string, quantity: number) => void;\n  clearCart: () => void;\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\nexport const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(cartReducer, { items: [], total: 0 });\n\n  // Load cart from localStorage on mount\n  useEffect(() => {\n    const savedCart = localStorage.getItem('cart');\n    if (savedCart) {\n      try {\n        const cartItems = JSON.parse(savedCart);\n        dispatch({ type: 'LOAD_CART', payload: cartItems });\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save cart to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cart', JSON.stringify(state.items));\n  }, [state.items]);\n\n  const addItem = (project: Project) => {\n    dispatch({ type: 'ADD_ITEM', payload: project });\n  };\n\n  const removeItem = (projectId: string) => {\n    dispatch({ type: 'REMOVE_ITEM', payload: projectId });\n  };\n\n  const updateQuantity = (projectId: string, quantity: number) => {\n    dispatch({ type: 'UPDATE_QUANTITY', payload: { id: projectId, quantity } });\n  };\n\n  const clearCart = () => {\n    dispatch({ type: 'CLEAR_CART' });\n  };\n\n  return (\n    <CartContext.Provider value={{ state, addItem, removeItem, updateQuantity, clearCart }}>\n      {children}\n    </CartContext.Provider>\n  );\n};\n\nexport const useCart = () => {\n  const context = useContext(CartContext);\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AA8BA,MAAM,cAAc,CAAC,OAAkB;IACrC,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAY;gBACf,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;gBAEnF,IAAI,cAAc;oBAChB,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACnC,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACjC;4BAAE,GAAG,IAAI;4BAAE,UAAU,KAAK,QAAQ,GAAG;wBAAE,IACvC;oBAEN,OAAO;wBACL,OAAO;wBACP,OAAO,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;oBACxF;gBACF;gBAEA,MAAM,WAAW;uBAAI,MAAM,KAAK;oBAAE;wBAAE,SAAS,OAAO,OAAO;wBAAE,UAAU;oBAAE;iBAAE;gBAC3E,OAAO;oBACL,OAAO;oBACP,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;gBACpF;YACF;QAEA,KAAK;YAAe;gBAClB,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO;gBAC9E,OAAO;oBACL,OAAO;oBACP,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;gBACpF;YACF;QAEA,KAAK;YAAmB;gBACtB,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACnC,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACjC;wBAAE,GAAG,IAAI;wBAAE,UAAU,KAAK,GAAG,CAAC,GAAG,OAAO,OAAO,CAAC,QAAQ;oBAAE,IAC1D,MACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG;gBAEjC,OAAO;oBACL,OAAO;oBACP,OAAO,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;gBACxF;YACF;QAEA,KAAK;YACH,OAAO;gBAAE,OAAO,EAAE;gBAAE,OAAO;YAAE;QAE/B,KAAK;YACH,OAAO;gBACL,OAAO,OAAO,OAAO;gBACrB,OAAO,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;YAC1F;QAEF;YACE,OAAO;IACX;AACF;AAUA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;;IAChF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,aAAa;QAAE,OAAO,EAAE;QAAE,OAAO;IAAE;IAExE,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,SAAS;wBAAE,MAAM;wBAAa,SAAS;oBAAU;gBACnD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACzD;YACF;QACF;iCAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,MAAM,KAAK;QACzD;iCAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,MAAM,UAAU,CAAC;QACf,SAAS;YAAE,MAAM;YAAY,SAAS;QAAQ;IAChD;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe,SAAS;QAAU;IACrD;IAEA,MAAM,iBAAiB,CAAC,WAAmB;QACzC,SAAS;YAAE,MAAM;YAAmB,SAAS;gBAAE,IAAI;gBAAW;YAAS;QAAE;IAC3E;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;IAChC;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAS;YAAY;YAAgB;QAAU;kBAClF;;;;;;AAGP;GA1Ca;KAAA;AA4CN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [theme, setTheme] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n\n    // Check for saved theme preference or default to 'light'\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n      setTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const initialTheme = systemPrefersDark ? 'dark' : 'light';\n      setTheme(initialTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    // Apply theme to document\n    const root = document.documentElement;\n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n\n    // Save theme preference\n    localStorage.setItem('theme', theme);\n  }, [theme, mounted]);\n\n  const toggleTheme = () => {\n    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;YAEX,yDAAyD;YACzD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,cAAc,CAAC,eAAe,WAAW,eAAe,MAAM,GAAG;gBACnE,SAAS;YACX,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;YACX;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,0BAA0B;YAC1B,MAAM,OAAO,SAAS,eAAe;YACrC,IAAI,UAAU,QAAQ;gBACpB,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;YAEA,wBAAwB;YACxB,aAAa,OAAO,CAAC,SAAS;QAChC;kCAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,SAAS,CAAA,YAAa,cAAc,UAAU,SAAS;IACzD;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP;GA3Ca;KAAA;AA6CN,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}