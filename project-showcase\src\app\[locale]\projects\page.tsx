'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import ThemeToggle from '@/components/ThemeToggle';

// Mock data for projects
const mockProjects = [
  {
    id: '1',
    title: 'E-commerce Mobile App',
    titleAr: 'تطبيق التجارة الإلكترونية',
    description: 'Modern e-commerce app with payment integration',
    descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع',
    price: 299,
    category: 'app',
    image: '/api/placeholder/400/300',
    technologies: ['React Native', 'Node.js', 'MongoDB'],
    features: ['User Authentication', 'Payment Gateway', 'Push Notifications'],
    featuresAr: ['مصادقة المستخدم', 'بوابة الدفع', 'الإشعارات الفورية'],
    demoUrl: '#',
    sourceCodeIncluded: true
  },
  {
    id: '2',
    title: 'Restaurant Website',
    titleAr: 'موقع مطعم',
    description: 'Responsive restaurant website with online ordering',
    descriptionAr: 'موقع مطعم متجاوب مع نظام الطلب عبر الإنترنت',
    price: 199,
    category: 'website',
    image: '/api/placeholder/400/300',
    technologies: ['Next.js', 'Tailwind CSS', 'Stripe'],
    features: ['Online Menu', 'Order Management', 'Table Booking'],
    featuresAr: ['قائمة طعام إلكترونية', 'إدارة الطلبات', 'حجز الطاولات'],
    demoUrl: '#',
    sourceCodeIncluded: true
  },
  {
    id: '3',
    title: 'Task Management App',
    titleAr: 'تطبيق إدارة المهام',
    description: 'Productivity app for team collaboration',
    descriptionAr: 'تطبيق إنتاجية للتعاون الجماعي',
    price: 249,
    category: 'app',
    image: '/api/placeholder/400/300',
    technologies: ['React', 'Firebase', 'Material-UI'],
    features: ['Team Collaboration', 'Real-time Updates', 'File Sharing'],
    featuresAr: ['التعاون الجماعي', 'التحديثات الفورية', 'مشاركة الملفات'],
    demoUrl: '#',
    sourceCodeIncluded: false
  },
  {
    id: '4',
    title: 'Portfolio Website',
    titleAr: 'موقع معرض أعمال',
    description: 'Creative portfolio website for designers',
    descriptionAr: 'موقع معرض أعمال إبداعي للمصممين',
    price: 149,
    category: 'website',
    image: '/api/placeholder/400/300',
    technologies: ['Vue.js', 'SCSS', 'Netlify'],
    features: ['Gallery', 'Contact Form', 'Blog'],
    featuresAr: ['معرض الصور', 'نموذج الاتصال', 'المدونة'],
    demoUrl: '#',
    sourceCodeIncluded: true
  },
  {
    id: '5',
    title: 'Learning Management System',
    titleAr: 'نظام إدارة التعلم',
    description: 'Complete LMS with video streaming',
    descriptionAr: 'نظام إدارة تعلم كامل مع بث الفيديو',
    price: 499,
    category: 'website',
    image: '/api/placeholder/400/300',
    technologies: ['Laravel', 'Vue.js', 'MySQL'],
    features: ['Video Streaming', 'Quiz System', 'Progress Tracking'],
    featuresAr: ['بث الفيديو', 'نظام الاختبارات', 'تتبع التقدم'],
    demoUrl: '#',
    sourceCodeIncluded: true
  },
  {
    id: '6',
    title: 'Fitness Tracker App',
    titleAr: 'تطبيق تتبع اللياقة',
    description: 'Health and fitness tracking mobile app',
    descriptionAr: 'تطبيق محمول لتتبع الصحة واللياقة',
    price: 199,
    category: 'app',
    image: '/api/placeholder/400/300',
    technologies: ['Flutter', 'Firebase', 'HealthKit'],
    features: ['Workout Tracking', 'Nutrition Log', 'Social Features'],
    featuresAr: ['تتبع التمارين', 'سجل التغذية', 'الميزات الاجتماعية'],
    demoUrl: '#',
    sourceCodeIncluded: false
  }
];

export default function ProjectsPage() {
  const params = useParams();
  const locale = params.locale as string;
  const { addItem, state } = useCart();
  const [projects, setProjects] = useState(mockProjects);
  const [filteredProjects, setFilteredProjects] = useState(mockProjects);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('newest');

  // Filter and search logic
  useEffect(() => {
    let filtered = projects;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(project => {
        const title = locale === 'ar' ? project.titleAr : project.title;
        const description = locale === 'ar' ? project.descriptionAr : project.description;
        return title.toLowerCase().includes(searchQuery.toLowerCase()) ||
               description.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }

    // Sort projects
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'name':
          const titleA = locale === 'ar' ? a.titleAr : a.title;
          const titleB = locale === 'ar' ? b.titleAr : b.title;
          return titleA.localeCompare(titleB);
        default:
          return 0;
      }
    });

    setFilteredProjects(filtered);
  }, [selectedCategory, searchQuery, sortBy, projects, locale]);

  const addToCart = (project: any) => {
    const cartProject = {
      id: project.id,
      title: project.title,
      titleAr: project.titleAr,
      price: project.price,
      category: project.category,
      image: project.image
    };
    addItem(cartProject);
    alert(locale === 'ar' ? 'تم إضافة المشروع إلى السلة!' : 'Project added to cart!');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">🦅</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  {locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'}
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <Link
                href={locale === 'en' ? '/ar/projects' : '/en/projects'}
                className="p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors"
              >
                {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}
              </Link>
              <Link
                href={`/${locale}/cart`}
                className="relative p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors"
              >
                🛒
                {state.items.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-amber-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {state.items.length}
                  </span>
                )}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {locale === 'ar' ? 'مشاريعنا' : 'Our Projects'}
          </h1>
          <p className="text-gray-600">
            {locale === 'ar' 
              ? 'اكتشف مجموعتنا الواسعة من التطبيقات والمواقع عالية الجودة'
              : 'Discover our wide collection of high-quality applications and websites'
            }
          </p>
        </div>

        {/* Filters and Search */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <input
              type="text"
              placeholder={locale === 'ar' ? 'البحث في المشاريع...' : 'Search projects...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
              🔍
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 items-center">
            {/* Category Filter */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">
                {locale === 'ar' ? 'الفئة:' : 'Category:'}
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{locale === 'ar' ? 'الكل' : 'All'}</option>
                <option value="app">{locale === 'ar' ? 'التطبيقات' : 'Applications'}</option>
                <option value="website">{locale === 'ar' ? 'المواقع' : 'Websites'}</option>
              </select>
            </div>

            {/* Sort Filter */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">
                {locale === 'ar' ? 'ترتيب حسب:' : 'Sort by:'}
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
              >
                <option value="newest">{locale === 'ar' ? 'الأحدث' : 'Newest'}</option>
                <option value="price-low">{locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}</option>
                <option value="price-high">{locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}</option>
                <option value="name">{locale === 'ar' ? 'الاسم' : 'Name'}</option>
              </select>
            </div>

            {/* Results Count */}
            <div className="text-sm text-gray-600">
              {locale === 'ar' 
                ? `${filteredProjects.length} مشروع`
                : `${filteredProjects.length} projects`
              }
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <div key={project.id} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              {/* Project Image */}
              <div className="aspect-video bg-gray-200 rounded-t-lg flex items-center justify-center">
                <span className="text-gray-500">📱 {project.category === 'app' ? 'App' : 'Website'}</span>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {locale === 'ar' ? project.titleAr : project.title}
                  </h3>
                  <span className="text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded">
                    {project.category === 'app' 
                      ? (locale === 'ar' ? 'تطبيق' : 'App')
                      : (locale === 'ar' ? 'موقع' : 'Website')
                    }
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4">
                  {locale === 'ar' ? project.descriptionAr : project.description}
                </p>

                {/* Technologies */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {project.technologies.slice(0, 3).map((tech, index) => (
                      <span key={index} className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded">
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded">
                        +{project.technologies.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    {locale === 'ar' ? 'الميزات:' : 'Features:'}
                  </h4>
                  <ul className="text-xs text-gray-600 space-y-1">
                    {(locale === 'ar' ? project.featuresAr : project.features).slice(0, 3).map((feature, index) => (
                      <li key={index}>• {feature}</li>
                    ))}
                  </ul>
                </div>

                {/* Price and Actions */}
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-blue-600">
                    ${project.price}
                  </div>
                  <div className="flex space-x-2">
                    <Link
                      href={`/${locale}/projects/${project.id}`}
                      className="px-3 py-1 text-sm border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition-colors"
                    >
                      {locale === 'ar' ? 'التفاصيل' : 'Details'}
                    </Link>
                    <button
                      onClick={() => addToCart(project)}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      {locale === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {locale === 'ar' ? 'لم يتم العثور على مشاريع' : 'No projects found'}
            </h3>
            <p className="text-gray-600">
              {locale === 'ar' 
                ? 'جرب تغيير معايير البحث أو الفلترة'
                : 'Try changing your search or filter criteria'
              }
            </p>
          </div>
        )}
      </main>
    </div>
  );
}
