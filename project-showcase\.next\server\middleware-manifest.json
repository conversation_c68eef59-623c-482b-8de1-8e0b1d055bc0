{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2c4b2d10._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1f9ae7ce.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(ar|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lUWxgVLk+iytvShC4NDy7l9epzU+8D4umr1LgNfS0GM=", "__NEXT_PREVIEW_MODE_ID": "5c46b6a0960dc6359850593b80c7182f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "37083d9c1d5211428c48132ee090e212d0cd7d811e18410cbb50fdd9818b93df", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6e4c742da3145d56d55469ebef413e1fa2cc2d8a088aa35073a0fa7a7289c3da"}}}, "sortedMiddleware": ["/"], "functions": {}}