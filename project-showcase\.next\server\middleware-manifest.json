{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2c4b2d10._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1f9ae7ce.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(ar|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "odDKEz++zWAcUeUmqqaXDSIYMcYjB0ypREcufW3Vces=", "__NEXT_PREVIEW_MODE_ID": "64b4ca3917002b36220c3dfc4759645a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a4b97be02f8275ac89b44c889b2256c4935de5fd740f03530f89f6a19c1dce69", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "04cfe5e1afc68a386898dd1e27d608d372fd527a5ac5983beb889931a173555b"}}}, "sortedMiddleware": ["/"], "functions": {}}