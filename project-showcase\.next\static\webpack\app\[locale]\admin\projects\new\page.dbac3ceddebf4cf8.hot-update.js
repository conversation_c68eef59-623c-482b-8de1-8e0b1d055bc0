"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/admin/projects/new/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/admin/projects/new/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/[locale]/admin/projects/new/page.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewProjectPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewProjectPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const locale = params.locale;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        titleAr: '',\n        description: '',\n        descriptionAr: '',\n        price: '',\n        category: 'app',\n        technologies: '',\n        features: '',\n        featuresAr: '',\n        demoUrl: '',\n        sourceCodeIncluded: true,\n        status: 'draft'\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === 'checkbox' ? e.target.checked : value\n            }));\n    };\n    const handleImageUpload = (files)=>{\n        if (!files) return;\n        const newFiles = Array.from(files).filter((file)=>{\n            if (!file.type.startsWith('image/')) {\n                alert(locale === 'ar' ? 'يرجى اختيار ملفات صور فقط' : 'Please select image files only');\n                return false;\n            }\n            if (file.size > 5 * 1024 * 1024) {\n                alert(locale === 'ar' ? 'حجم الصورة يجب أن يكون أقل من 5 ميجابايت' : 'Image size should be less than 5MB');\n                return false;\n            }\n            return true;\n        });\n        if (images.length + newFiles.length > 10) {\n            alert(locale === 'ar' ? 'يمكن رفع 10 صور كحد أقصى' : 'Maximum 10 images allowed');\n            return;\n        }\n        setImages((prev)=>[\n                ...prev,\n                ...newFiles\n            ]);\n        // Create previews\n        newFiles.forEach((file)=>{\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                setImagePreviews((prev)=>{\n                    var _e_target;\n                    return [\n                        ...prev,\n                        (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                    ];\n                });\n            };\n            reader.readAsDataURL(file);\n        });\n    };\n    const removeImage = (index)=>{\n        setImages((prev)=>prev.filter((_, i)=>i !== index));\n        setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === 'dragenter' || e.type === 'dragover') {\n            setDragActive(true);\n        } else if (e.type === 'dragleave') {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n            handleImageUpload(e.dataTransfer.files);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (images.length === 0) {\n            alert(locale === 'ar' ? 'يرجى إضافة صورة واحدة على الأقل للمشروع' : 'Please add at least one image for the project');\n            return;\n        }\n        setIsSubmitting(true);\n        // Create FormData for file upload\n        const submitData = new FormData();\n        // Add form fields\n        Object.entries(formData).forEach((param)=>{\n            let [key, value] = param;\n            submitData.append(key, value.toString());\n        });\n        // Add images\n        images.forEach((image, index)=>{\n            submitData.append(\"image_\".concat(index), image);\n        });\n        // Simulate API call with file upload\n        setTimeout(()=>{\n            setIsSubmitting(false);\n            alert(locale === 'ar' ? 'تم إضافة المشروع بنجاح مع الصور!' : 'Project added successfully with images!');\n            router.push(\"/\".concat(locale, \"/admin/projects\"));\n        }, 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\".concat(locale, \"/admin\"),\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83E\\uDD85\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-xl font-bold text-gray-900\",\n                                            children: locale === 'ar' ? 'إضافة مشروع جديد' : 'Add New Project'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\".concat(locale, \"/admin/projects\"),\n                                    className: \"text-amber-600 hover:text-amber-700 transition-colors\",\n                                    children: [\n                                        \"← \",\n                                        locale === 'ar' ? 'العودة للمشاريع' : 'Back to Projects'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: locale === 'ar' ? 'إضافة مشروع جديد' : 'Add New Project'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'اسم المشروع (إنجليزي)' : 'Project Title (English)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"title\",\n                                                    value: formData.title,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'اسم المشروع (عربي)' : 'Project Title (Arabic)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"titleAr\",\n                                                    value: formData.titleAr,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleInputChange,\n                                                    rows: 4,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"descriptionAr\",\n                                                    value: formData.descriptionAr,\n                                                    onChange: handleInputChange,\n                                                    rows: 4,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                locale === 'ar' ? 'صور المشروع' : 'Project Images',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-1\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed rounded-lg p-6 text-center transition-colors \".concat(dragActive ? 'border-amber-500 bg-amber-50' : 'border-gray-300 hover:border-amber-400'),\n                                            onDragEnter: handleDrag,\n                                            onDragLeave: handleDrag,\n                                            onDragOver: handleDrag,\n                                            onDrop: handleDrop,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl text-gray-400\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium text-gray-700\",\n                                                                children: locale === 'ar' ? 'اسحب الصور هنا أو انقر للاختيار' : 'Drag images here or click to select'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: locale === 'ar' ? 'PNG, JPG, GIF حتى 5 ميجابايت (10 صور كحد أقصى)' : 'PNG, JPG, GIF up to 5MB (Maximum 10 images)'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        multiple: true,\n                                                        accept: \"image/*\",\n                                                        onChange: (e)=>handleImageUpload(e.target.files),\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"image-upload\",\n                                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 cursor-pointer transition-colors\",\n                                                        children: locale === 'ar' ? 'اختيار الصور' : 'Choose Images'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                    children: locale === 'ar' ? \"الصور المحددة (\".concat(images.length, \")\") : \"Selected Images (\".concat(images.length, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                    children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: preview,\n                                                                    alt: \"Preview \".concat(index + 1),\n                                                                    className: \"w-full h-24 object-cover rounded-lg border border-gray-200\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>removeImage(index),\n                                                                    className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100\",\n                                                                    children: \"✕\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded\",\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: locale === 'ar' ? 'الصورة الأولى ستكون الصورة الرئيسية للمشروع' : 'The first image will be the main project image'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'السعر ($)' : 'Price ($)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    name: \"price\",\n                                                    value: formData.price,\n                                                    onChange: handleInputChange,\n                                                    min: \"0\",\n                                                    step: \"0.01\",\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'الفئة' : 'Category'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"category\",\n                                                    value: formData.category,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"app\",\n                                                            children: locale === 'ar' ? 'تطبيق' : 'Application'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"website\",\n                                                            children: locale === 'ar' ? 'موقع ويب' : 'Website'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: locale === 'ar' ? 'التقنيات المستخدمة (مفصولة بفواصل)' : 'Technologies Used (comma separated)'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            name: \"technologies\",\n                                            value: formData.technologies,\n                                            onChange: handleInputChange,\n                                            placeholder: \"React, Node.js, MongoDB\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'الميزات (إنجليزي - كل ميزة في سطر)' : 'Features (English - one per line)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"features\",\n                                                    value: formData.features,\n                                                    onChange: handleInputChange,\n                                                    rows: 6,\n                                                    placeholder: \"User Authentication Payment Gateway Push Notifications\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'الميزات (عربي - كل ميزة في سطر)' : 'Features (Arabic - one per line)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"featuresAr\",\n                                                    value: formData.featuresAr,\n                                                    onChange: handleInputChange,\n                                                    rows: 6,\n                                                    placeholder: \"مصادقة المستخدم بوابة الدفع الإشعارات الفورية\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: locale === 'ar' ? 'رابط العرض التجريبي (اختياري)' : 'Demo URL (optional)'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"url\",\n                                            name: \"demoUrl\",\n                                            value: formData.demoUrl,\n                                            onChange: handleInputChange,\n                                            placeholder: \"https://demo.example.com\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    name: \"sourceCodeIncluded\",\n                                                    checked: formData.sourceCodeIncluded,\n                                                    onChange: handleInputChange,\n                                                    className: \"rounded border-gray-300 text-amber-600 focus:ring-amber-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"ml-2 text-sm text-gray-700\",\n                                                    children: locale === 'ar' ? 'يشمل الكود المصدري' : 'Source code included'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: locale === 'ar' ? 'حالة المشروع' : 'Project Status'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"status\",\n                                                    value: formData.status,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-amber-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"draft\",\n                                                            children: locale === 'ar' ? 'مسودة' : 'Draft'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"active\",\n                                                            children: locale === 'ar' ? 'نشط' : 'Active'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                            children: isSubmitting ? locale === 'ar' ? 'جاري الحفظ...' : 'Saving...' : locale === 'ar' ? 'حفظ المشروع' : 'Save Project'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\".concat(locale, \"/admin/projects\"),\n                                            className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors\",\n                                            children: locale === 'ar' ? 'إلغاء' : 'Cancel'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\admin\\\\projects\\\\new\\\\page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProjectPage, \"HHMB2Sj1n3dVO3tZDAltCpMk/Js=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = NewProjectPage;\nvar _c;\n$RefreshReg$(_c, \"NewProjectPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/admin/projects/new/page.tsx\n"));

/***/ })

});