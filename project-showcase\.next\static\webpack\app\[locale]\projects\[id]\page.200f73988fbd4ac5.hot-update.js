"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/projects/[id]/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/[locale]/projects/[id]/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./src/contexts/CartContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Mock data - same as in projects page\nconst mockProjects = [\n    {\n        id: '1',\n        title: 'E-commerce Mobile App',\n        titleAr: 'تطبيق التجارة الإلكترونية',\n        description: 'Modern e-commerce app with payment integration and user-friendly interface. Built with React Native for cross-platform compatibility.',\n        descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع وواجهة سهلة الاستخدام. مبني بـ React Native للتوافق متعدد المنصات.',\n        price: 299,\n        category: 'app',\n        image: '/api/placeholder/600/400',\n        technologies: [\n            'React Native',\n            'Node.js',\n            'MongoDB',\n            'Stripe',\n            'Firebase'\n        ],\n        features: [\n            'User Authentication & Registration',\n            'Product Catalog with Search',\n            'Shopping Cart & Wishlist',\n            'Payment Gateway Integration',\n            'Push Notifications',\n            'Order Tracking',\n            'User Reviews & Ratings',\n            'Admin Dashboard'\n        ],\n        featuresAr: [\n            'مصادقة وتسجيل المستخدم',\n            'كتالوج المنتجات مع البحث',\n            'سلة التسوق وقائمة الأمنيات',\n            'تكامل بوابة الدفع',\n            'الإشعارات الفورية',\n            'تتبع الطلبات',\n            'تقييمات ومراجعات المستخدمين',\n            'لوحة تحكم الإدارة'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true,\n        documentation: true,\n        support: '6 months',\n        updates: '1 year'\n    }\n];\nfunction ProjectDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const locale = params.locale;\n    const projectId = params.id;\n    const { addItem } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    // Find project by ID\n    const project = mockProjects.find((p)=>p.id === projectId) || mockProjects[0];\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const addToCart = ()=>{\n        const cartProject = {\n            id: project.id,\n            title: project.title,\n            titleAr: project.titleAr,\n            price: project.price,\n            category: project.category,\n            image: project.image\n        };\n        for(let i = 0; i < quantity; i++){\n            addItem(cartProject);\n        }\n        alert(locale === 'ar' ? 'تم إضافة المشروع إلى السلة!' : 'Project added to cart!');\n    };\n    const buyNow = ()=>{\n        addToCart();\n        router.push(\"/\".concat(locale, \"/cart\"));\n    };\n    // Mock images for gallery\n    const projectImages = [\n        '/api/placeholder/600/400',\n        '/api/placeholder/600/400',\n        '/api/placeholder/600/400',\n        '/api/placeholder/600/400'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\".concat(locale),\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83E\\uDD85\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-xl font-bold text-gray-900\",\n                                            children: locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/projects\"),\n                                        className: \"text-blue-600 hover:text-blue-700 transition-colors\",\n                                        children: [\n                                            \"← \",\n                                            locale === 'ar' ? 'العودة للمشاريع' : 'Back to Projects'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: locale === 'en' ? \"/ar/projects/\".concat(projectId) : \"/en/projects/\".concat(projectId),\n                                        className: \"p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/cart\"),\n                                        className: \"relative p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                        children: \"\\uD83D\\uDED2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gray-200 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 text-4xl\",\n                                            children: [\n                                                \"\\uD83D\\uDCF1 \",\n                                                project.category === 'app' ? 'App Preview' : 'Website Preview'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2\",\n                                        children: projectImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedImage(index),\n                                                className: \"aspect-square bg-gray-200 rounded-lg flex items-center justify-center text-xs \".concat(selectedImage === index ? 'ring-2 ring-blue-500' : ''),\n                                                children: index + 1\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                        children: project.category === 'app' ? locale === 'ar' ? 'تطبيق' : 'Application' : locale === 'ar' ? 'موقع ويب' : 'Website'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    project.sourceCodeIncluded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: locale === 'ar' ? 'يشمل الكود المصدري' : 'Source Code Included'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: locale === 'ar' ? project.titleAr : project.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-lg\",\n                                                children: locale === 'ar' ? project.descriptionAr : project.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-b border-gray-200 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-blue-600 mb-2\",\n                                                children: [\n                                                    \"$\",\n                                                    project.price\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: locale === 'ar' ? 'سعر لمرة واحدة - ترخيص تجاري' : 'One-time price - Commercial license'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: locale === 'ar' ? 'الكمية:' : 'Quantity:'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center border border-gray-300 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                className: \"px-3 py-1 hover:bg-gray-100\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-4 py-1 border-x border-gray-300\",\n                                                                children: quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setQuantity(quantity + 1),\n                                                                className: \"px-3 py-1 hover:bg-gray-100\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: addToCart,\n                                                        className: \"flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                                        children: locale === 'ar' ? 'أضف إلى السلة' : 'Add to Cart'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: buyNow,\n                                                        className: \"flex-1 border border-blue-600 text-blue-600 py-3 px-6 rounded-lg hover:bg-blue-50 transition-colors font-medium\",\n                                                        children: locale === 'ar' ? 'اشتري الآن' : 'Buy Now'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this),\n                                            project.demoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: project.demoUrl,\n                                                className: \"block w-full text-center border border-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                children: locale === 'ar' ? 'عرض تجريبي' : 'Live Demo'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                children: locale === 'ar' ? 'ما يشمله المشروع:' : \"What's Included:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            locale === 'ar' ? 'الملفات المصدرية الكاملة' : 'Complete source files'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    project.sourceCodeIncluded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            locale === 'ar' ? 'الكود المصدري' : 'Source code'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    project.documentation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            locale === 'ar' ? 'التوثيق والدليل' : 'Documentation & guide'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            locale === 'ar' ? \"دعم فني لمدة \".concat(project.support) : \"\".concat(project.support, \" technical support\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-500 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            locale === 'ar' ? \"تحديثات مجانية لمدة \".concat(project.updates) : \"\".concat(project.updates, \" free updates\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600\",\n                                            children: locale === 'ar' ? 'الميزات' : 'Features'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700\",\n                                            children: locale === 'ar' ? 'التقنيات' : 'Technologies'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700\",\n                                            children: locale === 'ar' ? 'المتطلبات' : 'Requirements'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: (locale === 'ar' ? project.featuresAr : project.features).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-500 mr-2 mt-1\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: locale === 'ar' ? 'التقنيات المستخدمة:' : 'Technologies Used:'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: project.technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm\",\n                                        children: tech\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\[id]\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailPage, \"zFwgauc6/MUB1M4u33ccOskEq8Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart\n    ];\n});\n_c = ProjectDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/projects/[id]/page.tsx\n"));

/***/ })

});