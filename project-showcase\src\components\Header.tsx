'use client';

import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import ThemeToggle from './ThemeToggle';
import NoSSR from './NoSSR';

export default function Header() {
  const params = useParams();
  const locale = params.locale as string;
  const { state } = useCart();

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 transition-all duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href={`/${locale}`} className="flex items-center">
              <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">🦅</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                {locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'}
              </span>
            </Link>
          </div>

          {/* Navigation - Center */}
          <nav className="hidden md:flex space-x-8">
            <Link
              href={`/${locale}`}
              className="text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {locale === 'ar' ? 'الرئيسية' : 'Home'}
            </Link>
            <Link
              href={`/${locale}/projects`}
              className="text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {locale === 'ar' ? 'عرض المشاريع' : 'Projects'}
            </Link>
            <Link
              href={`/${locale}/about`}
              className="text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {locale === 'ar' ? 'من نحن' : 'About Us'}
            </Link>
            <Link
              href={`/${locale}/contact`}
              className="text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {locale === 'ar' ? 'اتصل بنا' : 'Contact'}
            </Link>
          </nav>

          {/* Right side buttons */}
          <div className="flex items-center space-x-4">
            <NoSSR>
              <ThemeToggle />
            </NoSSR>

            <Link
              href={`/${locale}/cart`}
              className="relative p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors"
            >
              🛒
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-amber-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {state.items.length}
                </span>
              )}
            </Link>

            <Link
              href={locale === 'en' ? '/ar' : '/en'}
              className="p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors"
            >
              {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}
            </Link>

            <Link
              href={`/${locale}/login`}
              className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {locale === 'ar' ? 'تسجيل الدخول' : 'Login'}
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button className="text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 p-2">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
