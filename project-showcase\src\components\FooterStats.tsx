'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function FooterStats() {
  const [mounted, setMounted] = useState(false);
  const [stats, setStats] = useState({
    visitors: 0,
    projects: 0,
    sales: 0
  });

  useEffect(() => {
    setMounted(true);
    
    const updateStats = () => {
      setStats({
        visitors: Math.floor(Math.random() * 10000) + 15000,
        projects: Math.floor(Math.random() * 50) + 120,
        sales: Math.floor(Math.random() * 500) + 850
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center justify-center space-x-8">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          <div className="w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          <div className="w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          <div className="w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex flex-wrap items-center justify-center gap-6 text-sm"
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 px-3 py-1 rounded-full"
      >
        <span className="text-blue-600 dark:text-blue-400">👥</span>
        <span className="font-medium text-blue-700 dark:text-blue-300">
          {stats.visitors.toLocaleString()} زائر
        </span>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="flex items-center space-x-2 bg-green-50 dark:bg-green-900/20 px-3 py-1 rounded-full"
      >
        <span className="text-green-600 dark:text-green-400">📱</span>
        <span className="font-medium text-green-700 dark:text-green-300">
          {stats.projects} مشروع
        </span>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="flex items-center space-x-2 bg-amber-50 dark:bg-amber-900/20 px-3 py-1 rounded-full"
      >
        <span className="text-amber-600 dark:text-amber-400">💰</span>
        <span className="font-medium text-amber-700 dark:text-amber-300">
          {stats.sales} عملية بيع
        </span>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="flex items-center space-x-2 text-gray-500 dark:text-gray-400"
      >
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        <span className="text-xs">مباشر</span>
      </motion.div>
    </motion.div>
  );
}
