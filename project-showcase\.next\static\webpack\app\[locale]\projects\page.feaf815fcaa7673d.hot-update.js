"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/projects/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/projects/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/projects/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./src/contexts/CartContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Mock data for projects\nconst mockProjects = [\n    {\n        id: '1',\n        title: 'E-commerce Mobile App',\n        titleAr: 'تطبيق التجارة الإلكترونية',\n        description: 'Modern e-commerce app with payment integration',\n        descriptionAr: 'تطبيق تجارة إلكترونية حديث مع تكامل الدفع',\n        price: 299,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'React Native',\n            'Node.js',\n            'MongoDB'\n        ],\n        features: [\n            'User Authentication',\n            'Payment Gateway',\n            'Push Notifications'\n        ],\n        featuresAr: [\n            'مصادقة المستخدم',\n            'بوابة الدفع',\n            'الإشعارات الفورية'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '2',\n        title: 'Restaurant Website',\n        titleAr: 'موقع مطعم',\n        description: 'Responsive restaurant website with online ordering',\n        descriptionAr: 'موقع مطعم متجاوب مع نظام الطلب عبر الإنترنت',\n        price: 199,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Next.js',\n            'Tailwind CSS',\n            'Stripe'\n        ],\n        features: [\n            'Online Menu',\n            'Order Management',\n            'Table Booking'\n        ],\n        featuresAr: [\n            'قائمة طعام إلكترونية',\n            'إدارة الطلبات',\n            'حجز الطاولات'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '3',\n        title: 'Task Management App',\n        titleAr: 'تطبيق إدارة المهام',\n        description: 'Productivity app for team collaboration',\n        descriptionAr: 'تطبيق إنتاجية للتعاون الجماعي',\n        price: 249,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'React',\n            'Firebase',\n            'Material-UI'\n        ],\n        features: [\n            'Team Collaboration',\n            'Real-time Updates',\n            'File Sharing'\n        ],\n        featuresAr: [\n            'التعاون الجماعي',\n            'التحديثات الفورية',\n            'مشاركة الملفات'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: false\n    },\n    {\n        id: '4',\n        title: 'Portfolio Website',\n        titleAr: 'موقع معرض أعمال',\n        description: 'Creative portfolio website for designers',\n        descriptionAr: 'موقع معرض أعمال إبداعي للمصممين',\n        price: 149,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Vue.js',\n            'SCSS',\n            'Netlify'\n        ],\n        features: [\n            'Gallery',\n            'Contact Form',\n            'Blog'\n        ],\n        featuresAr: [\n            'معرض الصور',\n            'نموذج الاتصال',\n            'المدونة'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '5',\n        title: 'Learning Management System',\n        titleAr: 'نظام إدارة التعلم',\n        description: 'Complete LMS with video streaming',\n        descriptionAr: 'نظام إدارة تعلم كامل مع بث الفيديو',\n        price: 499,\n        category: 'website',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Laravel',\n            'Vue.js',\n            'MySQL'\n        ],\n        features: [\n            'Video Streaming',\n            'Quiz System',\n            'Progress Tracking'\n        ],\n        featuresAr: [\n            'بث الفيديو',\n            'نظام الاختبارات',\n            'تتبع التقدم'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: true\n    },\n    {\n        id: '6',\n        title: 'Fitness Tracker App',\n        titleAr: 'تطبيق تتبع اللياقة',\n        description: 'Health and fitness tracking mobile app',\n        descriptionAr: 'تطبيق محمول لتتبع الصحة واللياقة',\n        price: 199,\n        category: 'app',\n        image: '/api/placeholder/400/300',\n        technologies: [\n            'Flutter',\n            'Firebase',\n            'HealthKit'\n        ],\n        features: [\n            'Workout Tracking',\n            'Nutrition Log',\n            'Social Features'\n        ],\n        featuresAr: [\n            'تتبع التمارين',\n            'سجل التغذية',\n            'الميزات الاجتماعية'\n        ],\n        demoUrl: '#',\n        sourceCodeIncluded: false\n    }\n];\nfunction ProjectsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const locale = params.locale;\n    const { addItem, state } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockProjects);\n    const [filteredProjects, setFilteredProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockProjects);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    // Filter and search logic\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            let filtered = projects;\n            // Filter by category\n            if (selectedCategory !== 'all') {\n                filtered = filtered.filter({\n                    \"ProjectsPage.useEffect\": (project)=>project.category === selectedCategory\n                }[\"ProjectsPage.useEffect\"]);\n            }\n            // Filter by search query\n            if (searchQuery) {\n                filtered = filtered.filter({\n                    \"ProjectsPage.useEffect\": (project)=>{\n                        const title = locale === 'ar' ? project.titleAr : project.title;\n                        const description = locale === 'ar' ? project.descriptionAr : project.description;\n                        return title.toLowerCase().includes(searchQuery.toLowerCase()) || description.toLowerCase().includes(searchQuery.toLowerCase());\n                    }\n                }[\"ProjectsPage.useEffect\"]);\n            }\n            // Sort projects\n            filtered = [\n                ...filtered\n            ].sort({\n                \"ProjectsPage.useEffect\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price-low':\n                            return a.price - b.price;\n                        case 'price-high':\n                            return b.price - a.price;\n                        case 'name':\n                            const titleA = locale === 'ar' ? a.titleAr : a.title;\n                            const titleB = locale === 'ar' ? b.titleAr : b.title;\n                            return titleA.localeCompare(titleB);\n                        default:\n                            return 0;\n                    }\n                }\n            }[\"ProjectsPage.useEffect\"]);\n            setFilteredProjects(filtered);\n        }\n    }[\"ProjectsPage.useEffect\"], [\n        selectedCategory,\n        searchQuery,\n        sortBy,\n        projects,\n        locale\n    ]);\n    const addToCart = (project)=>{\n        const cartProject = {\n            id: project.id,\n            title: project.title,\n            titleAr: project.titleAr,\n            price: project.price,\n            category: project.category,\n            image: project.image\n        };\n        addItem(cartProject);\n        alert(locale === 'ar' ? 'تم إضافة المشروع إلى السلة!' : 'Project added to cart!');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\".concat(locale),\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83E\\uDD85\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-xl font-bold text-gray-900\",\n                                            children: locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeToggle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: locale === 'en' ? '/ar/projects' : '/en/projects',\n                                        className: \"p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors\",\n                                        children: locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/cart\"),\n                                        className: \"relative p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors\",\n                                        children: [\n                                            \"\\uD83D\\uDED2\",\n                                            state.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -top-1 -right-1 bg-amber-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                                children: state.items.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: locale === 'ar' ? 'مشاريعنا' : 'Our Projects'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: locale === 'ar' ? 'اكتشف مجموعتنا الواسعة من التطبيقات والمواقع عالية الجودة' : 'Discover our wide collection of high-quality applications and websites'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: locale === 'ar' ? 'البحث في المشاريع...' : 'Search projects...',\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 right-3 flex items-center pointer-events-none\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: locale === 'ar' ? 'الفئة:' : 'Category:'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                className: \"px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: locale === 'ar' ? 'الكل' : 'All'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"app\",\n                                                        children: locale === 'ar' ? 'التطبيقات' : 'Applications'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"website\",\n                                                        children: locale === 'ar' ? 'المواقع' : 'Websites'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: locale === 'ar' ? 'ترتيب حسب:' : 'Sort by:'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"newest\",\n                                                        children: locale === 'ar' ? 'الأحدث' : 'Newest'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"price-low\",\n                                                        children: locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"price-high\",\n                                                        children: locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: locale === 'ar' ? 'الاسم' : 'Name'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: locale === 'ar' ? \"\".concat(filteredProjects.length, \" مشروع\") : \"\".concat(filteredProjects.length, \" projects\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gray-200 rounded-t-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: [\n                                                \"\\uD83D\\uDCF1 \",\n                                                project.category === 'app' ? 'App' : 'Website'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: locale === 'ar' ? project.titleAr : project.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                        children: project.category === 'app' ? locale === 'ar' ? 'تطبيق' : 'App' : locale === 'ar' ? 'موقع' : 'Website'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm mb-4\",\n                                                children: locale === 'ar' ? project.descriptionAr : project.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        project.technologies.slice(0, 3).map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded\",\n                                                                children: tech\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        project.technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded\",\n                                                            children: [\n                                                                \"+\",\n                                                                project.technologies.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                        children: locale === 'ar' ? 'الميزات:' : 'Features:'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-xs text-gray-600 space-y-1\",\n                                                        children: (locale === 'ar' ? project.featuresAr : project.features).slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    feature\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            project.price\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/\".concat(locale, \"/projects/\").concat(project.id),\n                                                                className: \"px-3 py-1 text-sm border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition-colors\",\n                                                                children: locale === 'ar' ? 'التفاصيل' : 'Details'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>addToCart(project),\n                                                                className: \"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                                                                children: locale === 'ar' ? 'أضف للسلة' : 'Add to Cart'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, project.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    filteredProjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: locale === 'ar' ? 'لم يتم العثور على مشاريع' : 'No projects found'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: locale === 'ar' ? 'جرب تغيير معايير البحث أو الفلترة' : 'Try changing your search or filter criteria'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Golden Eagle Company\\\\project-showcase\\\\src\\\\app\\\\[locale]\\\\projects\\\\page.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsPage, \"Z69HrK8yzxLZ3RhVxcg1Jh+nj6I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams,\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart\n    ];\n});\n_c = ProjectsPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vcHJvamVjdHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNmO0FBQ2U7QUFDSztBQUdqRCx5QkFBeUI7QUFDekIsTUFBTUssZUFBZTtJQUNuQjtRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztZQUFDO1lBQWdCO1lBQVc7U0FBVTtRQUNwREMsVUFBVTtZQUFDO1lBQXVCO1lBQW1CO1NBQXFCO1FBQzFFQyxZQUFZO1lBQUM7WUFBbUI7WUFBZTtTQUFvQjtRQUNuRUMsU0FBUztRQUNUQyxvQkFBb0I7SUFDdEI7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztZQUFDO1lBQVc7WUFBZ0I7U0FBUztRQUNuREMsVUFBVTtZQUFDO1lBQWU7WUFBb0I7U0FBZ0I7UUFDOURDLFlBQVk7WUFBQztZQUF3QjtZQUFpQjtTQUFlO1FBQ3JFQyxTQUFTO1FBQ1RDLG9CQUFvQjtJQUN0QjtJQUNBO1FBQ0VaLElBQUk7UUFDSkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxjQUFjO1lBQUM7WUFBUztZQUFZO1NBQWM7UUFDbERDLFVBQVU7WUFBQztZQUFzQjtZQUFxQjtTQUFlO1FBQ3JFQyxZQUFZO1lBQUM7WUFBbUI7WUFBcUI7U0FBaUI7UUFDdEVDLFNBQVM7UUFDVEMsb0JBQW9CO0lBQ3RCO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLGNBQWM7WUFBQztZQUFVO1lBQVE7U0FBVTtRQUMzQ0MsVUFBVTtZQUFDO1lBQVc7WUFBZ0I7U0FBTztRQUM3Q0MsWUFBWTtZQUFDO1lBQWM7WUFBaUI7U0FBVTtRQUN0REMsU0FBUztRQUNUQyxvQkFBb0I7SUFDdEI7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztZQUFDO1lBQVc7WUFBVTtTQUFRO1FBQzVDQyxVQUFVO1lBQUM7WUFBbUI7WUFBZTtTQUFvQjtRQUNqRUMsWUFBWTtZQUFDO1lBQWM7WUFBbUI7U0FBYztRQUM1REMsU0FBUztRQUNUQyxvQkFBb0I7SUFDdEI7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztZQUFDO1lBQVc7WUFBWTtTQUFZO1FBQ2xEQyxVQUFVO1lBQUM7WUFBb0I7WUFBaUI7U0FBa0I7UUFDbEVDLFlBQVk7WUFBQztZQUFpQjtZQUFlO1NBQXFCO1FBQ2xFQyxTQUFTO1FBQ1RDLG9CQUFvQjtJQUN0QjtDQUNEO0FBRWMsU0FBU0M7O0lBQ3RCLE1BQU1DLFNBQVNqQiwwREFBU0E7SUFDeEIsTUFBTWtCLFNBQVNELE9BQU9DLE1BQU07SUFDNUIsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLEtBQUssRUFBRSxHQUFHbkIsOERBQU9BO0lBQ2xDLE1BQU0sQ0FBQ29CLFVBQVVDLFlBQVksR0FBR3pCLCtDQUFRQSxDQUFDSztJQUN6QyxNQUFNLENBQUNxQixrQkFBa0JDLG9CQUFvQixHQUFHM0IsK0NBQVFBLENBQUNLO0lBQ3pELE1BQU0sQ0FBQ3VCLGtCQUFrQkMsb0JBQW9CLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM4QixhQUFhQyxlQUFlLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNnQyxRQUFRQyxVQUFVLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUVyQywwQkFBMEI7SUFDMUJDLGdEQUFTQTtrQ0FBQztZQUNSLElBQUlpQyxXQUFXVjtZQUVmLHFCQUFxQjtZQUNyQixJQUFJSSxxQkFBcUIsT0FBTztnQkFDOUJNLFdBQVdBLFNBQVNDLE1BQU07OENBQUNDLENBQUFBLFVBQVdBLFFBQVF4QixRQUFRLEtBQUtnQjs7WUFDN0Q7WUFFQSx5QkFBeUI7WUFDekIsSUFBSUUsYUFBYTtnQkFDZkksV0FBV0EsU0FBU0MsTUFBTTs4Q0FBQ0MsQ0FBQUE7d0JBQ3pCLE1BQU03QixRQUFRYyxXQUFXLE9BQU9lLFFBQVE1QixPQUFPLEdBQUc0QixRQUFRN0IsS0FBSzt3QkFDL0QsTUFBTUUsY0FBY1ksV0FBVyxPQUFPZSxRQUFRMUIsYUFBYSxHQUFHMEIsUUFBUTNCLFdBQVc7d0JBQ2pGLE9BQU9GLE1BQU04QixXQUFXLEdBQUdDLFFBQVEsQ0FBQ1IsWUFBWU8sV0FBVyxPQUNwRDVCLFlBQVk0QixXQUFXLEdBQUdDLFFBQVEsQ0FBQ1IsWUFBWU8sV0FBVztvQkFDbkU7O1lBQ0Y7WUFFQSxnQkFBZ0I7WUFDaEJILFdBQVc7bUJBQUlBO2FBQVMsQ0FBQ0ssSUFBSTswQ0FBQyxDQUFDQyxHQUFHQztvQkFDaEMsT0FBUVQ7d0JBQ04sS0FBSzs0QkFDSCxPQUFPUSxFQUFFN0IsS0FBSyxHQUFHOEIsRUFBRTlCLEtBQUs7d0JBQzFCLEtBQUs7NEJBQ0gsT0FBTzhCLEVBQUU5QixLQUFLLEdBQUc2QixFQUFFN0IsS0FBSzt3QkFDMUIsS0FBSzs0QkFDSCxNQUFNK0IsU0FBU3JCLFdBQVcsT0FBT21CLEVBQUVoQyxPQUFPLEdBQUdnQyxFQUFFakMsS0FBSzs0QkFDcEQsTUFBTW9DLFNBQVN0QixXQUFXLE9BQU9vQixFQUFFakMsT0FBTyxHQUFHaUMsRUFBRWxDLEtBQUs7NEJBQ3BELE9BQU9tQyxPQUFPRSxhQUFhLENBQUNEO3dCQUM5Qjs0QkFDRSxPQUFPO29CQUNYO2dCQUNGOztZQUVBaEIsb0JBQW9CTztRQUN0QjtpQ0FBRztRQUFDTjtRQUFrQkU7UUFBYUU7UUFBUVI7UUFBVUg7S0FBTztJQUU1RCxNQUFNd0IsWUFBWSxDQUFDVDtRQUNqQixNQUFNVSxjQUFjO1lBQ2xCeEMsSUFBSThCLFFBQVE5QixFQUFFO1lBQ2RDLE9BQU82QixRQUFRN0IsS0FBSztZQUNwQkMsU0FBUzRCLFFBQVE1QixPQUFPO1lBQ3hCRyxPQUFPeUIsUUFBUXpCLEtBQUs7WUFDcEJDLFVBQVV3QixRQUFReEIsUUFBUTtZQUMxQkMsT0FBT3VCLFFBQVF2QixLQUFLO1FBQ3RCO1FBQ0FTLFFBQVF3QjtRQUNSQyxNQUFNMUIsV0FBVyxPQUFPLGdDQUFnQztJQUMxRDtJQUVBLHFCQUNFLDhEQUFDMkI7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFPRCxXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUMvQyxrREFBSUE7b0NBQUNpRCxNQUFNLElBQVcsT0FBUDlCO29DQUFVNEIsV0FBVTs7c0RBQ2xDLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0c7Z0RBQUtILFdBQVU7MERBQStCOzs7Ozs7Ozs7OztzREFFakQsOERBQUNHOzRDQUFLSCxXQUFVO3NEQUNiNUIsV0FBVyxPQUFPLHNCQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSS9DLDhEQUFDMkI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDSTs7Ozs7a0RBQ0QsOERBQUNuRCxrREFBSUE7d0NBQ0hpRCxNQUFNOUIsV0FBVyxPQUFPLGlCQUFpQjt3Q0FDekM0QixXQUFVO2tEQUVUNUIsV0FBVyxPQUFPLGlCQUFpQjs7Ozs7O2tEQUV0Qyw4REFBQ25CLGtEQUFJQTt3Q0FDSGlELE1BQU0sSUFBVyxPQUFQOUIsUUFBTzt3Q0FDakI0QixXQUFVOzs0Q0FDWDs0Q0FFRTFCLE1BQU0rQixLQUFLLENBQUNDLE1BQU0sR0FBRyxtQkFDcEIsOERBQUNIO2dEQUFLSCxXQUFVOzBEQUNiMUIsTUFBTStCLEtBQUssQ0FBQ0MsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTakMsOERBQUNDO2dCQUFLUCxXQUFVOztrQ0FFZCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FDWDVCLFdBQVcsT0FBTyxhQUFhOzs7Ozs7MENBRWxDLDhEQUFDcUM7Z0NBQUVULFdBQVU7MENBQ1Y1QixXQUFXLE9BQ1IsOERBQ0E7Ozs7Ozs7Ozs7OztrQ0FNUiw4REFBQzJCO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDVTt3Q0FDQ0MsTUFBSzt3Q0FDTEMsYUFBYXhDLFdBQVcsT0FBTyx5QkFBeUI7d0NBQ3hEeUMsT0FBT2hDO3dDQUNQaUMsVUFBVSxDQUFDQyxJQUFNakMsZUFBZWlDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDOUNiLFdBQVU7Ozs7OztrREFFWiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQW1FOzs7Ozs7Ozs7Ozs7MENBTXBGLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBRWIsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2lCO2dEQUFNakIsV0FBVTswREFDZDVCLFdBQVcsT0FBTyxXQUFXOzs7Ozs7MERBRWhDLDhEQUFDOEM7Z0RBQ0NMLE9BQU9sQztnREFDUG1DLFVBQVUsQ0FBQ0MsSUFBTW5DLG9CQUFvQm1DLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnREFDbkRiLFdBQVU7O2tFQUVWLDhEQUFDbUI7d0RBQU9OLE9BQU07a0VBQU96QyxXQUFXLE9BQU8sU0FBUzs7Ozs7O2tFQUNoRCw4REFBQytDO3dEQUFPTixPQUFNO2tFQUFPekMsV0FBVyxPQUFPLGNBQWM7Ozs7OztrRUFDckQsOERBQUMrQzt3REFBT04sT0FBTTtrRUFBV3pDLFdBQVcsT0FBTyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzNELDhEQUFDMkI7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDaUI7Z0RBQU1qQixXQUFVOzBEQUNkNUIsV0FBVyxPQUFPLGVBQWU7Ozs7OzswREFFcEMsOERBQUM4QztnREFDQ0wsT0FBTzlCO2dEQUNQK0IsVUFBVSxDQUFDQyxJQUFNL0IsVUFBVStCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnREFDekNiLFdBQVU7O2tFQUVWLDhEQUFDbUI7d0RBQU9OLE9BQU07a0VBQVV6QyxXQUFXLE9BQU8sV0FBVzs7Ozs7O2tFQUNyRCw4REFBQytDO3dEQUFPTixPQUFNO2tFQUFhekMsV0FBVyxPQUFPLDJCQUEyQjs7Ozs7O2tFQUN4RSw4REFBQytDO3dEQUFPTixPQUFNO2tFQUFjekMsV0FBVyxPQUFPLDJCQUEyQjs7Ozs7O2tFQUN6RSw4REFBQytDO3dEQUFPTixPQUFNO2tFQUFRekMsV0FBVyxPQUFPLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLdEQsOERBQUMyQjt3Q0FBSUMsV0FBVTtrREFDWjVCLFdBQVcsT0FDUixHQUEyQixPQUF4QkssaUJBQWlCNkIsTUFBTSxFQUFDLFlBQzNCLEdBQTJCLE9BQXhCN0IsaUJBQWlCNkIsTUFBTSxFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT3JDLDhEQUFDUDt3QkFBSUMsV0FBVTtrQ0FDWnZCLGlCQUFpQjJDLEdBQUcsQ0FBQyxDQUFDakMsd0JBQ3JCLDhEQUFDWTtnQ0FBcUJDLFdBQVU7O2tEQUU5Qiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNHOzRDQUFLSCxXQUFVOztnREFBZ0I7Z0RBQUliLFFBQVF4QixRQUFRLEtBQUssUUFBUSxRQUFROzs7Ozs7Ozs7Ozs7a0RBSTNFLDhEQUFDb0M7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNxQjt3REFBR3JCLFdBQVU7a0VBQ1g1QixXQUFXLE9BQU9lLFFBQVE1QixPQUFPLEdBQUc0QixRQUFRN0IsS0FBSzs7Ozs7O2tFQUVwRCw4REFBQzZDO3dEQUFLSCxXQUFVO2tFQUNiYixRQUFReEIsUUFBUSxLQUFLLFFBQ2pCUyxXQUFXLE9BQU8sVUFBVSxRQUM1QkEsV0FBVyxPQUFPLFNBQVM7Ozs7Ozs7Ozs7OzswREFLcEMsOERBQUNxQztnREFBRVQsV0FBVTswREFDVjVCLFdBQVcsT0FBT2UsUUFBUTFCLGFBQWEsR0FBRzBCLFFBQVEzQixXQUFXOzs7Ozs7MERBSWhFLDhEQUFDdUM7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNEO29EQUFJQyxXQUFVOzt3REFDWmIsUUFBUXRCLFlBQVksQ0FBQ3lELEtBQUssQ0FBQyxHQUFHLEdBQUdGLEdBQUcsQ0FBQyxDQUFDRyxNQUFNQyxzQkFDM0MsOERBQUNyQjtnRUFBaUJILFdBQVU7MEVBQ3pCdUI7K0RBRFFDOzs7Ozt3REFJWnJDLFFBQVF0QixZQUFZLENBQUN5QyxNQUFNLEdBQUcsbUJBQzdCLDhEQUFDSDs0REFBS0gsV0FBVTs7Z0VBQXNEO2dFQUNsRWIsUUFBUXRCLFlBQVksQ0FBQ3lDLE1BQU0sR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU94Qyw4REFBQ1A7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUNYNUIsV0FBVyxPQUFPLGFBQWE7Ozs7OztrRUFFbEMsOERBQUNzRDt3REFBRzFCLFdBQVU7a0VBQ1gsQ0FBQzVCLFdBQVcsT0FBT2UsUUFBUXBCLFVBQVUsR0FBR29CLFFBQVFyQixRQUFRLEVBQUV3RCxLQUFLLENBQUMsR0FBRyxHQUFHRixHQUFHLENBQUMsQ0FBQ08sU0FBU0gsc0JBQ25GLDhEQUFDSTs7b0VBQWU7b0VBQUdEOzsrREFBVkg7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTWYsOERBQUN6QjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzs0REFBbUM7NERBQzlDYixRQUFRekIsS0FBSzs7Ozs7OztrRUFFakIsOERBQUNxQzt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUMvQyxrREFBSUE7Z0VBQ0hpRCxNQUFNLElBQXVCZixPQUFuQmYsUUFBTyxjQUF1QixPQUFYZSxRQUFROUIsRUFBRTtnRUFDdkMyQyxXQUFVOzBFQUVUNUIsV0FBVyxPQUFPLGFBQWE7Ozs7OzswRUFFbEMsOERBQUN5RDtnRUFDQ0MsU0FBUyxJQUFNbEMsVUFBVVQ7Z0VBQ3pCYSxXQUFVOzBFQUVUNUIsV0FBVyxPQUFPLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0JBcEVqQ2UsUUFBUTlCLEVBQUU7Ozs7Ozs7Ozs7b0JBOEV2Qm9CLGlCQUFpQjZCLE1BQU0sS0FBSyxtQkFDM0IsOERBQUNQO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQWdCOzs7Ozs7MENBQy9CLDhEQUFDcUI7Z0NBQUdyQixXQUFVOzBDQUNYNUIsV0FBVyxPQUFPLDZCQUE2Qjs7Ozs7OzBDQUVsRCw4REFBQ3FDO2dDQUFFVCxXQUFVOzBDQUNWNUIsV0FBVyxPQUNSLHNDQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRbEI7R0FsUndCRjs7UUFDUGhCLHNEQUFTQTtRQUVHQywwREFBT0E7OztLQUhaZSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTdXJmYWNlXFxHb2xkZW4gRWFnbGUgQ29tcGFueVxccHJvamVjdC1zaG93Y2FzZVxcc3JjXFxhcHBcXFtsb2NhbGVdXFxwcm9qZWN0c1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IHVzZVBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VDYXJ0IH0gZnJvbSAnQC9jb250ZXh0cy9DYXJ0Q29udGV4dCc7XG5pbXBvcnQgSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9IZWFkZXInO1xuXG4vLyBNb2NrIGRhdGEgZm9yIHByb2plY3RzXG5jb25zdCBtb2NrUHJvamVjdHMgPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHRpdGxlOiAnRS1jb21tZXJjZSBNb2JpbGUgQXBwJyxcbiAgICB0aXRsZUFyOiAn2KrYt9io2YrZgiDYp9mE2KrYrNin2LHYqSDYp9mE2KXZhNmD2KrYsdmI2YbZitipJyxcbiAgICBkZXNjcmlwdGlvbjogJ01vZGVybiBlLWNvbW1lcmNlIGFwcCB3aXRoIHBheW1lbnQgaW50ZWdyYXRpb24nLFxuICAgIGRlc2NyaXB0aW9uQXI6ICfYqti32KjZitmCINiq2KzYp9ix2Kkg2KXZhNmD2KrYsdmI2YbZitipINit2K/ZitirINmF2Lkg2KrZg9in2YXZhCDYp9mE2K/Zgdi5JyxcbiAgICBwcmljZTogMjk5LFxuICAgIGNhdGVnb3J5OiAnYXBwJyxcbiAgICBpbWFnZTogJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzMwMCcsXG4gICAgdGVjaG5vbG9naWVzOiBbJ1JlYWN0IE5hdGl2ZScsICdOb2RlLmpzJywgJ01vbmdvREInXSxcbiAgICBmZWF0dXJlczogWydVc2VyIEF1dGhlbnRpY2F0aW9uJywgJ1BheW1lbnQgR2F0ZXdheScsICdQdXNoIE5vdGlmaWNhdGlvbnMnXSxcbiAgICBmZWF0dXJlc0FyOiBbJ9mF2LXYp9iv2YLYqSDYp9mE2YXYs9iq2K7Yr9mFJywgJ9io2YjYp9io2Kkg2KfZhNiv2YHYuScsICfYp9mE2KXYtNi52KfYsdin2Kog2KfZhNmB2YjYsdmK2KknXSxcbiAgICBkZW1vVXJsOiAnIycsXG4gICAgc291cmNlQ29kZUluY2x1ZGVkOiB0cnVlXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIHRpdGxlOiAnUmVzdGF1cmFudCBXZWJzaXRlJyxcbiAgICB0aXRsZUFyOiAn2YXZiNmC2Lkg2YXYt9i52YUnLFxuICAgIGRlc2NyaXB0aW9uOiAnUmVzcG9uc2l2ZSByZXN0YXVyYW50IHdlYnNpdGUgd2l0aCBvbmxpbmUgb3JkZXJpbmcnLFxuICAgIGRlc2NyaXB0aW9uQXI6ICfZhdmI2YLYuSDZhdi32LnZhSDZhdiq2KzYp9mI2Kgg2YXYuSDZhti42KfZhSDYp9mE2LfZhNioINi52KjYsSDYp9mE2KXZhtiq2LHZhtiqJyxcbiAgICBwcmljZTogMTk5LFxuICAgIGNhdGVnb3J5OiAnd2Vic2l0ZScsXG4gICAgaW1hZ2U6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC8zMDAnLFxuICAgIHRlY2hub2xvZ2llczogWydOZXh0LmpzJywgJ1RhaWx3aW5kIENTUycsICdTdHJpcGUnXSxcbiAgICBmZWF0dXJlczogWydPbmxpbmUgTWVudScsICdPcmRlciBNYW5hZ2VtZW50JywgJ1RhYmxlIEJvb2tpbmcnXSxcbiAgICBmZWF0dXJlc0FyOiBbJ9mC2KfYptmF2Kkg2LfYudin2YUg2KXZhNmD2KrYsdmI2YbZitipJywgJ9il2K/Yp9ix2Kkg2KfZhNi32YTYqNin2KonLCAn2K3YrNiyINin2YTYt9in2YjZhNin2KonXSxcbiAgICBkZW1vVXJsOiAnIycsXG4gICAgc291cmNlQ29kZUluY2x1ZGVkOiB0cnVlXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIHRpdGxlOiAnVGFzayBNYW5hZ2VtZW50IEFwcCcsXG4gICAgdGl0bGVBcjogJ9iq2LfYqNmK2YIg2KXYr9in2LHYqSDYp9mE2YXZh9in2YUnLFxuICAgIGRlc2NyaXB0aW9uOiAnUHJvZHVjdGl2aXR5IGFwcCBmb3IgdGVhbSBjb2xsYWJvcmF0aW9uJyxcbiAgICBkZXNjcmlwdGlvbkFyOiAn2KrYt9io2YrZgiDYpdmG2KrYp9is2YrYqSDZhNmE2KrYudin2YjZhiDYp9mE2KzZhdin2LnZiicsXG4gICAgcHJpY2U6IDI0OSxcbiAgICBjYXRlZ29yeTogJ2FwcCcsXG4gICAgaW1hZ2U6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC8zMDAnLFxuICAgIHRlY2hub2xvZ2llczogWydSZWFjdCcsICdGaXJlYmFzZScsICdNYXRlcmlhbC1VSSddLFxuICAgIGZlYXR1cmVzOiBbJ1RlYW0gQ29sbGFib3JhdGlvbicsICdSZWFsLXRpbWUgVXBkYXRlcycsICdGaWxlIFNoYXJpbmcnXSxcbiAgICBmZWF0dXJlc0FyOiBbJ9in2YTYqti52KfZiNmGINin2YTYrNmF2KfYudmKJywgJ9in2YTYqtit2K/Zitir2KfYqiDYp9mE2YHZiNix2YrYqScsICfZhdi02KfYsdmD2Kkg2KfZhNmF2YTZgdin2KonXSxcbiAgICBkZW1vVXJsOiAnIycsXG4gICAgc291cmNlQ29kZUluY2x1ZGVkOiBmYWxzZVxuICB9LFxuICB7XG4gICAgaWQ6ICc0JyxcbiAgICB0aXRsZTogJ1BvcnRmb2xpbyBXZWJzaXRlJyxcbiAgICB0aXRsZUFyOiAn2YXZiNmC2Lkg2YXYudix2LYg2KPYudmF2KfZhCcsXG4gICAgZGVzY3JpcHRpb246ICdDcmVhdGl2ZSBwb3J0Zm9saW8gd2Vic2l0ZSBmb3IgZGVzaWduZXJzJyxcbiAgICBkZXNjcmlwdGlvbkFyOiAn2YXZiNmC2Lkg2YXYudix2LYg2KPYudmF2KfZhCDYpdio2K/Yp9i52Yog2YTZhNmF2LXZhdmF2YrZhicsXG4gICAgcHJpY2U6IDE0OSxcbiAgICBjYXRlZ29yeTogJ3dlYnNpdGUnLFxuICAgIGltYWdlOiAnL2FwaS9wbGFjZWhvbGRlci80MDAvMzAwJyxcbiAgICB0ZWNobm9sb2dpZXM6IFsnVnVlLmpzJywgJ1NDU1MnLCAnTmV0bGlmeSddLFxuICAgIGZlYXR1cmVzOiBbJ0dhbGxlcnknLCAnQ29udGFjdCBGb3JtJywgJ0Jsb2cnXSxcbiAgICBmZWF0dXJlc0FyOiBbJ9mF2LnYsdi2INin2YTYtdmI2LEnLCAn2YbZhdmI2LDYrCDYp9mE2KfYqti12KfZhCcsICfYp9mE2YXYr9mI2YbYqSddLFxuICAgIGRlbW9Vcmw6ICcjJyxcbiAgICBzb3VyY2VDb2RlSW5jbHVkZWQ6IHRydWVcbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgdGl0bGU6ICdMZWFybmluZyBNYW5hZ2VtZW50IFN5c3RlbScsXG4gICAgdGl0bGVBcjogJ9mG2LjYp9mFINil2K/Yp9ix2Kkg2KfZhNiq2LnZhNmFJyxcbiAgICBkZXNjcmlwdGlvbjogJ0NvbXBsZXRlIExNUyB3aXRoIHZpZGVvIHN0cmVhbWluZycsXG4gICAgZGVzY3JpcHRpb25BcjogJ9mG2LjYp9mFINil2K/Yp9ix2Kkg2KrYudmE2YUg2YPYp9mF2YQg2YXYuSDYqNirINin2YTZgdmK2K/ZitmIJyxcbiAgICBwcmljZTogNDk5LFxuICAgIGNhdGVnb3J5OiAnd2Vic2l0ZScsXG4gICAgaW1hZ2U6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC8zMDAnLFxuICAgIHRlY2hub2xvZ2llczogWydMYXJhdmVsJywgJ1Z1ZS5qcycsICdNeVNRTCddLFxuICAgIGZlYXR1cmVzOiBbJ1ZpZGVvIFN0cmVhbWluZycsICdRdWl6IFN5c3RlbScsICdQcm9ncmVzcyBUcmFja2luZyddLFxuICAgIGZlYXR1cmVzQXI6IFsn2KjYqyDYp9mE2YHZitiv2YrZiCcsICfZhti42KfZhSDYp9mE2KfYrtiq2KjYp9ix2KfYqicsICfYqtiq2KjYuSDYp9mE2KrZgtiv2YUnXSxcbiAgICBkZW1vVXJsOiAnIycsXG4gICAgc291cmNlQ29kZUluY2x1ZGVkOiB0cnVlXG4gIH0sXG4gIHtcbiAgICBpZDogJzYnLFxuICAgIHRpdGxlOiAnRml0bmVzcyBUcmFja2VyIEFwcCcsXG4gICAgdGl0bGVBcjogJ9iq2LfYqNmK2YIg2KrYqtio2Lkg2KfZhNmE2YrYp9mC2KknLFxuICAgIGRlc2NyaXB0aW9uOiAnSGVhbHRoIGFuZCBmaXRuZXNzIHRyYWNraW5nIG1vYmlsZSBhcHAnLFxuICAgIGRlc2NyaXB0aW9uQXI6ICfYqti32KjZitmCINmF2K3ZhdmI2YQg2YTYqtiq2KjYuSDYp9mE2LXYrdipINmI2KfZhNmE2YrYp9mC2KknLFxuICAgIHByaWNlOiAxOTksXG4gICAgY2F0ZWdvcnk6ICdhcHAnLFxuICAgIGltYWdlOiAnL2FwaS9wbGFjZWhvbGRlci80MDAvMzAwJyxcbiAgICB0ZWNobm9sb2dpZXM6IFsnRmx1dHRlcicsICdGaXJlYmFzZScsICdIZWFsdGhLaXQnXSxcbiAgICBmZWF0dXJlczogWydXb3Jrb3V0IFRyYWNraW5nJywgJ051dHJpdGlvbiBMb2cnLCAnU29jaWFsIEZlYXR1cmVzJ10sXG4gICAgZmVhdHVyZXNBcjogWyfYqtiq2KjYuSDYp9mE2KrZhdin2LHZitmGJywgJ9iz2KzZhCDYp9mE2KrYutiw2YrYqScsICfYp9mE2YXZitiy2KfYqiDYp9mE2KfYrNiq2YXYp9i52YrYqSddLFxuICAgIGRlbW9Vcmw6ICcjJyxcbiAgICBzb3VyY2VDb2RlSW5jbHVkZWQ6IGZhbHNlXG4gIH1cbl07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2plY3RzUGFnZSgpIHtcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKCk7XG4gIGNvbnN0IGxvY2FsZSA9IHBhcmFtcy5sb2NhbGUgYXMgc3RyaW5nO1xuICBjb25zdCB7IGFkZEl0ZW0sIHN0YXRlIH0gPSB1c2VDYXJ0KCk7XG4gIGNvbnN0IFtwcm9qZWN0cywgc2V0UHJvamVjdHNdID0gdXNlU3RhdGUobW9ja1Byb2plY3RzKTtcbiAgY29uc3QgW2ZpbHRlcmVkUHJvamVjdHMsIHNldEZpbHRlcmVkUHJvamVjdHNdID0gdXNlU3RhdGUobW9ja1Byb2plY3RzKTtcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGUoJ2FsbCcpO1xuICBjb25zdCBbc2VhcmNoUXVlcnksIHNldFNlYXJjaFF1ZXJ5XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NvcnRCeSwgc2V0U29ydEJ5XSA9IHVzZVN0YXRlKCduZXdlc3QnKTtcblxuICAvLyBGaWx0ZXIgYW5kIHNlYXJjaCBsb2dpY1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCBmaWx0ZXJlZCA9IHByb2plY3RzO1xuXG4gICAgLy8gRmlsdGVyIGJ5IGNhdGVnb3J5XG4gICAgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgIT09ICdhbGwnKSB7XG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihwcm9qZWN0ID0+IHByb2plY3QuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnkpO1xuICAgIH1cblxuICAgIC8vIEZpbHRlciBieSBzZWFyY2ggcXVlcnlcbiAgICBpZiAoc2VhcmNoUXVlcnkpIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHByb2plY3QgPT4ge1xuICAgICAgICBjb25zdCB0aXRsZSA9IGxvY2FsZSA9PT0gJ2FyJyA/IHByb2plY3QudGl0bGVBciA6IHByb2plY3QudGl0bGU7XG4gICAgICAgIGNvbnN0IGRlc2NyaXB0aW9uID0gbG9jYWxlID09PSAnYXInID8gcHJvamVjdC5kZXNjcmlwdGlvbkFyIDogcHJvamVjdC5kZXNjcmlwdGlvbjtcbiAgICAgICAgcmV0dXJuIHRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKSk7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBTb3J0IHByb2plY3RzXG4gICAgZmlsdGVyZWQgPSBbLi4uZmlsdGVyZWRdLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgIHN3aXRjaCAoc29ydEJ5KSB7XG4gICAgICAgIGNhc2UgJ3ByaWNlLWxvdyc6XG4gICAgICAgICAgcmV0dXJuIGEucHJpY2UgLSBiLnByaWNlO1xuICAgICAgICBjYXNlICdwcmljZS1oaWdoJzpcbiAgICAgICAgICByZXR1cm4gYi5wcmljZSAtIGEucHJpY2U7XG4gICAgICAgIGNhc2UgJ25hbWUnOlxuICAgICAgICAgIGNvbnN0IHRpdGxlQSA9IGxvY2FsZSA9PT0gJ2FyJyA/IGEudGl0bGVBciA6IGEudGl0bGU7XG4gICAgICAgICAgY29uc3QgdGl0bGVCID0gbG9jYWxlID09PSAnYXInID8gYi50aXRsZUFyIDogYi50aXRsZTtcbiAgICAgICAgICByZXR1cm4gdGl0bGVBLmxvY2FsZUNvbXBhcmUodGl0bGVCKTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gMDtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHNldEZpbHRlcmVkUHJvamVjdHMoZmlsdGVyZWQpO1xuICB9LCBbc2VsZWN0ZWRDYXRlZ29yeSwgc2VhcmNoUXVlcnksIHNvcnRCeSwgcHJvamVjdHMsIGxvY2FsZV0pO1xuXG4gIGNvbnN0IGFkZFRvQ2FydCA9IChwcm9qZWN0OiBhbnkpID0+IHtcbiAgICBjb25zdCBjYXJ0UHJvamVjdCA9IHtcbiAgICAgIGlkOiBwcm9qZWN0LmlkLFxuICAgICAgdGl0bGU6IHByb2plY3QudGl0bGUsXG4gICAgICB0aXRsZUFyOiBwcm9qZWN0LnRpdGxlQXIsXG4gICAgICBwcmljZTogcHJvamVjdC5wcmljZSxcbiAgICAgIGNhdGVnb3J5OiBwcm9qZWN0LmNhdGVnb3J5LFxuICAgICAgaW1hZ2U6IHByb2plY3QuaW1hZ2VcbiAgICB9O1xuICAgIGFkZEl0ZW0oY2FydFByb2plY3QpO1xuICAgIGFsZXJ0KGxvY2FsZSA9PT0gJ2FyJyA/ICfYqtmFINil2LbYp9mB2Kkg2KfZhNmF2LTYsdmI2Lkg2KXZhNmJINin2YTYs9mE2KkhJyA6ICdQcm9qZWN0IGFkZGVkIHRvIGNhcnQhJyk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZVwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93LXNtIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2AvJHtsb2NhbGV9YH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYW1iZXItNjAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtbGdcIj7wn6aFPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyA/ICfYtNix2YPYqSDYp9mE2YbYs9ixINin2YTYsNmH2KjZiicgOiAnR29sZGVuIEVhZ2xlIENvbXBhbnknfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8VGhlbWVUb2dnbGUgLz5cbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBocmVmPXtsb2NhbGUgPT09ICdlbicgPyAnL2FyL3Byb2plY3RzJyA6ICcvZW4vcHJvamVjdHMnfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWFtYmVyLTYwMCBkYXJrOmhvdmVyOnRleHQtYW1iZXItNDAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdlbicgPyAn8J+HrvCfh7Yg2KfZhNi52LHYqNmK2KknIDogJ/Cfh7rwn4e4IEVuZ2xpc2gnfVxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj17YC8ke2xvY2FsZX0vY2FydGB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgcC0yIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtYW1iZXItNjAwIGRhcms6aG92ZXI6dGV4dC1hbWJlci00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg8J+bklxuICAgICAgICAgICAgICAgIHtzdGF0ZS5pdGVtcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMSAtcmlnaHQtMSBiZy1hbWJlci02MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCBoLTUgdy01IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzdGF0ZS5pdGVtcy5sZW5ndGh9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgey8qIFBhZ2UgSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyA/ICfZhdi02KfYsdmK2LnZhtinJyA6ICdPdXIgUHJvamVjdHMnfVxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyBcbiAgICAgICAgICAgICAgPyAn2KfZg9iq2LTZgSDZhdis2YXZiNi52KrZhtinINin2YTZiNin2LPYudipINmF2YYg2KfZhNiq2LfYqNmK2YLYp9iqINmI2KfZhNmF2YjYp9mC2Lkg2LnYp9mE2YrYqSDYp9mE2KzZiNiv2KknXG4gICAgICAgICAgICAgIDogJ0Rpc2NvdmVyIG91ciB3aWRlIGNvbGxlY3Rpb24gb2YgaGlnaC1xdWFsaXR5IGFwcGxpY2F0aW9ucyBhbmQgd2Vic2l0ZXMnXG4gICAgICAgICAgICB9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmlsdGVycyBhbmQgU2VhcmNoICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTggc3BhY2UteS00XCI+XG4gICAgICAgICAgey8qIFNlYXJjaCBCYXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2KjYrdirINmB2Yog2KfZhNmF2LTYp9ix2YrYuS4uLicgOiAnU2VhcmNoIHByb2plY3RzLi4uJ31cbiAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFF1ZXJ5fVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFF1ZXJ5KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCByaWdodC0zIGZsZXggaXRlbXMtY2VudGVyIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgICAg8J+UjVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRmlsdGVycyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC00IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgey8qIENhdGVnb3J5IEZpbHRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9in2YTZgdim2Kk6JyA6ICdDYXRlZ29yeTonfVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkQ2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZENhdGVnb3J5KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj57bG9jYWxlID09PSAnYXInID8gJ9in2YTZg9mEJyA6ICdBbGwnfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhcHBcIj57bG9jYWxlID09PSAnYXInID8gJ9in2YTYqti32KjZitmC2KfYqicgOiAnQXBwbGljYXRpb25zJ308L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwid2Vic2l0ZVwiPntsb2NhbGUgPT09ICdhcicgPyAn2KfZhNmF2YjYp9mC2LknIDogJ1dlYnNpdGVzJ308L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFNvcnQgRmlsdGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcicgPyAn2KrYsdiq2YrYqCDYrdiz2Kg6JyA6ICdTb3J0IGJ5Oid9XG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17c29ydEJ5fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U29ydEJ5KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJuZXdlc3RcIj57bG9jYWxlID09PSAnYXInID8gJ9in2YTYo9it2K/YqycgOiAnTmV3ZXN0J308L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicHJpY2UtbG93XCI+e2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2LPYudixOiDZhdmGINin2YTYo9mC2YQg2YTZhNij2LnZhNmJJyA6ICdQcmljZTogTG93IHRvIEhpZ2gnfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJwcmljZS1oaWdoXCI+e2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2LPYudixOiDZhdmGINin2YTYo9i52YTZiSDZhNmE2KPZgtmEJyA6ICdQcmljZTogSGlnaCB0byBMb3cnfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJuYW1lXCI+e2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2KfYs9mFJyA6ICdOYW1lJ308L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFJlc3VsdHMgQ291bnQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInIFxuICAgICAgICAgICAgICAgID8gYCR7ZmlsdGVyZWRQcm9qZWN0cy5sZW5ndGh9INmF2LTYsdmI2LlgXG4gICAgICAgICAgICAgICAgOiBgJHtmaWx0ZXJlZFByb2plY3RzLmxlbmd0aH0gcHJvamVjdHNgXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUHJvamVjdHMgR3JpZCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAge2ZpbHRlcmVkUHJvamVjdHMubWFwKChwcm9qZWN0KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17cHJvamVjdC5pZH0gY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3dcIj5cbiAgICAgICAgICAgICAgey8qIFByb2plY3QgSW1hZ2UgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXNwZWN0LXZpZGVvIGJnLWdyYXktMjAwIHJvdW5kZWQtdC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7wn5OxIHtwcm9qZWN0LmNhdGVnb3J5ID09PSAnYXBwJyA/ICdBcHAnIDogJ1dlYnNpdGUnfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFByb2plY3QgQ29udGVudCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gcHJvamVjdC50aXRsZUFyIDogcHJvamVjdC50aXRsZX1cbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHB4LTIgcHktMSBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAge3Byb2plY3QuY2F0ZWdvcnkgPT09ICdhcHAnIFxuICAgICAgICAgICAgICAgICAgICAgID8gKGxvY2FsZSA9PT0gJ2FyJyA/ICfYqti32KjZitmCJyA6ICdBcHAnKVxuICAgICAgICAgICAgICAgICAgICAgIDogKGxvY2FsZSA9PT0gJ2FyJyA/ICfZhdmI2YLYuScgOiAnV2Vic2l0ZScpXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gcHJvamVjdC5kZXNjcmlwdGlvbkFyIDogcHJvamVjdC5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgICB7LyogVGVjaG5vbG9naWVzICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICB7cHJvamVjdC50ZWNobm9sb2dpZXMuc2xpY2UoMCwgMykubWFwKCh0ZWNoLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQteHMgcHgtMiBweS0xIGJnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3RlY2h9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAge3Byb2plY3QudGVjaG5vbG9naWVzLmxlbmd0aCA+IDMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgcHgtMiBweS0xIGJnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgK3twcm9qZWN0LnRlY2hub2xvZ2llcy5sZW5ndGggLSAzfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIEZlYXR1cmVzICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcicgPyAn2KfZhNmF2YrYstin2Ko6JyA6ICdGZWF0dXJlczonfVxuICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIHsobG9jYWxlID09PSAnYXInID8gcHJvamVjdC5mZWF0dXJlc0FyIDogcHJvamVjdC5mZWF0dXJlcykuc2xpY2UoMCwgMykubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fT7igKIge2ZlYXR1cmV9PC9saT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFByaWNlIGFuZCBBY3Rpb25zICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICR7cHJvamVjdC5wcmljZX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvJHtsb2NhbGV9L3Byb2plY3RzLyR7cHJvamVjdC5pZH1gfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIGJvcmRlciBib3JkZXItYmx1ZS02MDAgdGV4dC1ibHVlLTYwMCByb3VuZGVkIGhvdmVyOmJnLWJsdWUtNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2KrZgdin2LXZitmEJyA6ICdEZXRhaWxzJ31cbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gYWRkVG9DYXJ0KHByb2plY3QpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZCBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9ij2LbZgSDZhNmE2LPZhNipJyA6ICdBZGQgdG8gQ2FydCd9XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBFbXB0eSBTdGF0ZSAqL31cbiAgICAgICAge2ZpbHRlcmVkUHJvamVjdHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG1iLTRcIj7wn5SNPC9kaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyA/ICfZhNmFINmK2KrZhSDYp9mE2LnYq9mI2LEg2LnZhNmJINmF2LTYp9ix2YrYuScgOiAnTm8gcHJvamVjdHMgZm91bmQnfVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyBcbiAgICAgICAgICAgICAgICA/ICfYrNix2Kgg2KrYutmK2YrYsSDZhdi52KfZitmK2LEg2KfZhNio2K3YqyDYo9mIINin2YTZgdmE2KrYsdipJ1xuICAgICAgICAgICAgICAgIDogJ1RyeSBjaGFuZ2luZyB5b3VyIHNlYXJjaCBvciBmaWx0ZXIgY3JpdGVyaWEnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvbWFpbj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkxpbmsiLCJ1c2VQYXJhbXMiLCJ1c2VDYXJ0IiwibW9ja1Byb2plY3RzIiwiaWQiLCJ0aXRsZSIsInRpdGxlQXIiLCJkZXNjcmlwdGlvbiIsImRlc2NyaXB0aW9uQXIiLCJwcmljZSIsImNhdGVnb3J5IiwiaW1hZ2UiLCJ0ZWNobm9sb2dpZXMiLCJmZWF0dXJlcyIsImZlYXR1cmVzQXIiLCJkZW1vVXJsIiwic291cmNlQ29kZUluY2x1ZGVkIiwiUHJvamVjdHNQYWdlIiwicGFyYW1zIiwibG9jYWxlIiwiYWRkSXRlbSIsInN0YXRlIiwicHJvamVjdHMiLCJzZXRQcm9qZWN0cyIsImZpbHRlcmVkUHJvamVjdHMiLCJzZXRGaWx0ZXJlZFByb2plY3RzIiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5Iiwic29ydEJ5Iiwic2V0U29ydEJ5IiwiZmlsdGVyZWQiLCJmaWx0ZXIiLCJwcm9qZWN0IiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInNvcnQiLCJhIiwiYiIsInRpdGxlQSIsInRpdGxlQiIsImxvY2FsZUNvbXBhcmUiLCJhZGRUb0NhcnQiLCJjYXJ0UHJvamVjdCIsImFsZXJ0IiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwiaHJlZiIsInNwYW4iLCJUaGVtZVRvZ2dsZSIsIml0ZW1zIiwibGVuZ3RoIiwibWFpbiIsImgxIiwicCIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJsYWJlbCIsInNlbGVjdCIsIm9wdGlvbiIsIm1hcCIsImgzIiwic2xpY2UiLCJ0ZWNoIiwiaW5kZXgiLCJoNCIsInVsIiwiZmVhdHVyZSIsImxpIiwiYnV0dG9uIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/projects/page.tsx\n"));

/***/ })

});