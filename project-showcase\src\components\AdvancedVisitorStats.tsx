'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface StatCard {
  title: string;
  value: number;
  change: number;
  icon: string;
  color: string;
  bgGradient: string;
}

export default function AdvancedVisitorStats() {
  const [mounted, setMounted] = useState(false);
  const [stats, setStats] = useState<StatCard[]>([]);

  useEffect(() => {
    setMounted(true);
    
    const generateStats = () => {
      const newStats: StatCard[] = [
        {
          title: 'إجمالي الزوار',
          value: Math.floor(Math.random() * 50000) + 25000,
          change: Math.floor(Math.random() * 20) + 5,
          icon: '👥',
          color: 'text-blue-600 dark:text-blue-400',
          bgGradient: 'from-blue-500 to-blue-600'
        },
        {
          title: 'زوار اليوم',
          value: Math.floor(Math.random() * 1000) + 200,
          change: Math.floor(Math.random() * 15) + 2,
          icon: '🌍',
          color: 'text-green-600 dark:text-green-400',
          bgGradient: 'from-green-500 to-green-600'
        },
        {
          title: 'المشاهدات',
          value: Math.floor(Math.random() * 100000) + 50000,
          change: Math.floor(Math.random() * 25) + 8,
          icon: '📊',
          color: 'text-purple-600 dark:text-purple-400',
          bgGradient: 'from-purple-500 to-purple-600'
        },
        {
          title: 'معدل البقاء',
          value: Math.floor(Math.random() * 30) + 120, // seconds
          change: Math.floor(Math.random() * 10) + 3,
          icon: '⏱️',
          color: 'text-amber-600 dark:text-amber-400',
          bgGradient: 'from-amber-500 to-amber-600'
        }
      ];
      setStats(newStats);
    };

    generateStats();
    const interval = setInterval(generateStats, 45000);
    
    return () => clearInterval(interval);
  }, []);

  if (!mounted) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
              <div className="w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
            </div>
            <div className="w-20 h-8 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
            <div className="w-24 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${stat.bgGradient} flex items-center justify-center text-white text-xl group-hover:scale-110 transition-transform duration-300`}>
              {stat.icon}
            </div>
            <div className={`text-sm font-medium px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400`}>
              +{stat.change}%
            </div>
          </div>

          {/* Value */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
            className={`text-3xl font-bold ${stat.color} mb-2`}
          >
            {stat.title === 'معدل البقاء' 
              ? `${Math.floor(stat.value / 60)}:${(stat.value % 60).toString().padStart(2, '0')}`
              : stat.value.toLocaleString()
            }
          </motion.div>

          {/* Title */}
          <div className="text-gray-600 dark:text-gray-300 font-medium">
            {stat.title}
          </div>

          {/* Progress Bar */}
          <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${Math.min((stat.value / (stat.title === 'معدل البقاء' ? 300 : 100000)) * 100, 100)}%` }}
              transition={{ duration: 2, delay: 1 + index * 0.1 }}
              className={`h-2 rounded-full bg-gradient-to-r ${stat.bgGradient}`}
            ></motion.div>
          </div>

          {/* Sparkline Effect */}
          <div className="mt-3 flex items-end justify-between h-8 gap-1">
            {Array.from({ length: 7 }, (_, i) => {
              const height = Math.random() * 100;
              return (
                <motion.div
                  key={i}
                  initial={{ height: 0 }}
                  animate={{ height: `${height}%` }}
                  transition={{ duration: 1, delay: 2 + i * 0.05 }}
                  className={`w-1 bg-gradient-to-t ${stat.bgGradient} rounded-full opacity-60`}
                ></motion.div>
              );
            })}
          </div>
        </motion.div>
      ))}
    </div>
  );
}
