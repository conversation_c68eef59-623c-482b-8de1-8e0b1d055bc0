{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/components/VisitorCounter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface VisitorStats {\n  totalVisitors: number;\n  todayVisitors: number;\n  onlineVisitors: number;\n}\n\nexport default function VisitorCounter() {\n  const [stats, setStats] = useState<VisitorStats>({\n    totalVisitors: 0,\n    todayVisitors: 0,\n    onlineVisitors: 0\n  });\n\n  useEffect(() => {\n    // Simulate visitor tracking\n    const trackVisitor = () => {\n      // Get existing stats from localStorage\n      const existingStats = localStorage.getItem('visitorStats');\n      let currentStats: VisitorStats = {\n        totalVisitors: 2847,\n        todayVisitors: 156,\n        onlineVisitors: 12\n      };\n\n      if (existingStats) {\n        currentStats = JSON.parse(existingStats);\n      }\n\n      // Check if this is a new visitor today\n      const lastVisit = localStorage.getItem('lastVisit');\n      const today = new Date().toDateString();\n      \n      if (lastVisit !== today) {\n        currentStats.todayVisitors += 1;\n        currentStats.totalVisitors += 1;\n        localStorage.setItem('lastVisit', today);\n      }\n\n      // Simulate online visitors (random number between 8-20)\n      currentStats.onlineVisitors = Math.floor(Math.random() * 13) + 8;\n\n      // Save updated stats\n      localStorage.setItem('visitorStats', JSON.stringify(currentStats));\n      setStats(currentStats);\n    };\n\n    trackVisitor();\n\n    // Update online visitors every 30 seconds\n    const interval = setInterval(() => {\n      setStats(prev => ({\n        ...prev,\n        onlineVisitors: Math.floor(Math.random() * 13) + 8\n      }));\n    }, 30000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\n      <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n        إحصائيات الزوار\n      </h3>\n      <div className=\"space-y-2\">\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-xs text-gray-600 dark:text-gray-400\">إجمالي الزوار:</span>\n          <span className=\"text-sm font-semibold text-gray-900 dark:text-white\">\n            {stats.totalVisitors.toLocaleString()}\n          </span>\n        </div>\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-xs text-gray-600 dark:text-gray-400\">زوار اليوم:</span>\n          <span className=\"text-sm font-semibold text-blue-600 dark:text-blue-400\">\n            {stats.todayVisitors}\n          </span>\n        </div>\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-xs text-gray-600 dark:text-gray-400\">متصل الآن:</span>\n          <div className=\"flex items-center\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse\"></div>\n            <span className=\"text-sm font-semibold text-green-600 dark:text-green-400\">\n              {stats.onlineVisitors}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC/C,eAAe;QACf,eAAe;QACf,gBAAgB;IAClB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,MAAM,eAAe;YACnB,uCAAuC;YACvC,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAA6B;gBAC/B,eAAe;gBACf,eAAe;gBACf,gBAAgB;YAClB;YAEA,IAAI,eAAe;gBACjB,eAAe,KAAK,KAAK,CAAC;YAC5B;YAEA,uCAAuC;YACvC,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,MAAM,QAAQ,IAAI,OAAO,YAAY;YAErC,IAAI,cAAc,OAAO;gBACvB,aAAa,aAAa,IAAI;gBAC9B,aAAa,aAAa,IAAI;gBAC9B,aAAa,OAAO,CAAC,aAAa;YACpC;YAEA,wDAAwD;YACxD,aAAa,cAAc,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;YAE/D,qBAAqB;YACrB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YACpD,SAAS;QACX;QAEA;QAEA,0CAA0C;QAC1C,MAAM,WAAW,YAAY;YAC3B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBACnD,CAAC;QACH,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA4D;;;;;;0BAG1E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;0CAC3D,8OAAC;gCAAK,WAAU;0CACb,MAAM,aAAa,CAAC,cAAc;;;;;;;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;0CAC3D,8OAAC;gCAAK,WAAU;0CACb,MAAM,aAAa;;;;;;;;;;;;kCAGxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;0CAC3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDACb,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/components/NoSSR.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface NoSSRProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\nexport default function NoSSR({ children, fallback = null }: NoSSRProps) {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return <>{fallback}</>;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,MAAM,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from '@/contexts/ThemeContext';\nimport NoSSR from './NoSSR';\n\ninterface ThemeToggleProps {\n  className?: string;\n}\n\nfunction ThemeToggleContent({ className = '' }: ThemeToggleProps) {\n  const { theme, toggleTheme } = useTheme();\n\n  return (\n    <div className={`flex items-center ${className}`}>\n      {/* Toggle Switch */}\n      <button\n        onClick={toggleTheme}\n        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${\n          theme === 'dark' ? 'bg-amber-600' : 'bg-gray-300'\n        }`}\n        title={theme === 'light' ? 'تفعيل الوضع الداكن' : 'تفعيل الوضع الفاتح'}\n        aria-label={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}\n      >\n        <span className=\"sr-only\">\n          {theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}\n        </span>\n\n        {/* Toggle Circle */}\n        <span\n          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 shadow-sm ${\n            theme === 'dark' ? 'translate-x-6' : 'translate-x-1'\n          }`}\n        >\n          {/* Icon inside the circle */}\n          <span className=\"flex h-full w-full items-center justify-center text-xs\">\n            {theme === 'light' ? '☀️' : '🌙'}\n          </span>\n        </span>\n      </button>\n\n      {/* Optional Label */}\n      <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400 hidden sm:block\">\n        {theme === 'light' ? 'فاتح' : 'داكن'}\n      </span>\n    </div>\n  );\n}\n\nexport default function ThemeToggle({ className = '' }: ThemeToggleProps) {\n  return (\n    <NoSSR\n      fallback={\n        <div className={`flex items-center ${className}`}>\n          <div className=\"relative inline-flex h-6 w-11 items-center rounded-full bg-gray-300\">\n            <div className=\"inline-block h-4 w-4 transform rounded-full bg-white translate-x-1 shadow-sm\">\n              <span className=\"flex h-full w-full items-center justify-center text-xs\">☀️</span>\n            </div>\n          </div>\n          <span className=\"ml-2 text-sm text-gray-600 hidden sm:block\">فاتح</span>\n        </div>\n      }\n    >\n      <ThemeToggleContent className={className} />\n    </NoSSR>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA,SAAS,mBAAmB,EAAE,YAAY,EAAE,EAAoB;IAC9D,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEtC,qBACE,8OAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;;0BAE9C,8OAAC;gBACC,SAAS;gBACT,WAAW,CAAC,gMAAgM,EAC1M,UAAU,SAAS,iBAAiB,eACpC;gBACF,OAAO,UAAU,UAAU,uBAAuB;gBAClD,cAAY,UAAU,UAAU,wBAAwB;;kCAExD,8OAAC;wBAAK,WAAU;kCACb,UAAU,UAAU,wBAAwB;;;;;;kCAI/C,8OAAC;wBACC,WAAW,CAAC,iGAAiG,EAC3G,UAAU,SAAS,kBAAkB,iBACrC;kCAGF,cAAA,8OAAC;4BAAK,WAAU;sCACb,UAAU,UAAU,OAAO;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAK,WAAU;0BACb,UAAU,UAAU,SAAS;;;;;;;;;;;;AAItC;AAEe,SAAS,YAAY,EAAE,YAAY,EAAE,EAAoB;IACtE,qBACE,8OAAC,2HAAA,CAAA,UAAK;QACJ,wBACE,8OAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;;8BAC9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAyD;;;;;;;;;;;;;;;;8BAG7E,8OAAC;oBAAK,WAAU;8BAA6C;;;;;;;;;;;;kBAIjE,cAAA,8OAAC;YAAmB,WAAW;;;;;;;;;;;AAGrC", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\nimport { useCart } from '@/contexts/CartContext';\nimport ThemeToggle from './ThemeToggle';\nimport NoSSR from './NoSSR';\n\nexport default function Header() {\n  const params = useParams();\n  const locale = params.locale as string;\n  const { state } = useCart();\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 transition-all duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href={`/${locale}`} className=\"flex items-center\">\n              <div className=\"w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">🦅</span>\n              </div>\n              <span className=\"ml-2 text-xl font-bold text-gray-900 dark:text-white\">\n                {locale === 'ar' ? 'شركة النسر الذهبي' : 'Golden Eagle Company'}\n              </span>\n            </Link>\n          </div>\n\n          {/* Navigation - Center */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link\n              href={`/${locale}`}\n              className=\"text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              {locale === 'ar' ? 'الرئيسية' : 'Home'}\n            </Link>\n            <Link\n              href={`/${locale}/projects`}\n              className=\"text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              {locale === 'ar' ? 'عرض المشاريع' : 'Projects'}\n            </Link>\n            <Link\n              href={`/${locale}/about`}\n              className=\"text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              {locale === 'ar' ? 'من نحن' : 'About Us'}\n            </Link>\n            <Link\n              href={`/${locale}/contact`}\n              className=\"text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              {locale === 'ar' ? 'اتصل بنا' : 'Contact'}\n            </Link>\n          </nav>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            <NoSSR>\n              <ThemeToggle />\n            </NoSSR>\n\n            <Link\n              href={`/${locale}/cart`}\n              className=\"relative p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors\"\n            >\n              🛒\n              {state.items.length > 0 && (\n                <span className=\"absolute -top-1 -right-1 bg-amber-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {state.items.length}\n                </span>\n              )}\n            </Link>\n\n            <Link\n              href={locale === 'en' ? '/ar' : '/en'}\n              className=\"p-2 text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 transition-colors\"\n            >\n              {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}\n            </Link>\n\n            <Link\n              href={`/${locale}/login`}\n              className=\"bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              {locale === 'ar' ? 'تسجيل الدخول' : 'Login'}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button className=\"text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 p-2\">\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;4BAAE,WAAU;;8CAClC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CACb,WAAW,OAAO,sBAAsB;;;;;;;;;;;;;;;;;kCAM/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,QAAQ;gCAClB,WAAU;0CAET,WAAW,OAAO,aAAa;;;;;;0CAElC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC;gCAC3B,WAAU;0CAET,WAAW,OAAO,iBAAiB;;;;;;0CAEtC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;gCACxB,WAAU;0CAET,WAAW,OAAO,WAAW;;;;;;0CAEhC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;gCAC1B,WAAU;0CAET,WAAW,OAAO,aAAa;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,UAAK;0CACJ,cAAA,8OAAC,iIAAA,CAAA,UAAW;;;;;;;;;;0CAGd,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC;gCACvB,WAAU;;oCACX;oCAEE,MAAM,KAAK,CAAC,MAAM,GAAG,mBACpB,8OAAC;wCAAK,WAAU;kDACb,MAAM,KAAK,CAAC,MAAM;;;;;;;;;;;;0CAKzB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,WAAW,OAAO,QAAQ;gCAChC,WAAU;0CAET,WAAW,OAAO,iBAAiB;;;;;;0CAGtC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;gCACxB,WAAU;0CAET,WAAW,OAAO,iBAAiB;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/components/VisitorStats.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface StatItem {\n  icon: string;\n  value: number;\n  label: string;\n  color: string;\n  bgColor: string;\n}\n\nexport default function VisitorStats() {\n  const [mounted, setMounted] = useState(false);\n  const [stats, setStats] = useState<StatItem[]>([\n    {\n      icon: '👥',\n      value: 0,\n      label: 'إجمالي الزوار',\n      color: 'text-blue-600 dark:text-blue-400',\n      bgColor: 'bg-blue-100 dark:bg-blue-900/30'\n    },\n    {\n      icon: '🌍',\n      value: 0,\n      label: 'زوار اليوم',\n      color: 'text-green-600 dark:text-green-400',\n      bgColor: 'bg-green-100 dark:bg-green-900/30'\n    },\n    {\n      icon: '📊',\n      value: 0,\n      label: 'المشاهدات',\n      color: 'text-purple-600 dark:text-purple-400',\n      bgColor: 'bg-purple-100 dark:bg-purple-900/30'\n    },\n    {\n      icon: '⭐',\n      value: 0,\n      label: 'التقييمات',\n      color: 'text-amber-600 dark:text-amber-400',\n      bgColor: 'bg-amber-100 dark:bg-amber-900/30'\n    }\n  ]);\n\n  useEffect(() => {\n    setMounted(true);\n    \n    // محاكاة جلب البيانات من API\n    const fetchStats = () => {\n      const newStats = [\n        {\n          icon: '👥',\n          value: Math.floor(Math.random() * 10000) + 5000,\n          label: 'إجمالي الزوار',\n          color: 'text-blue-600 dark:text-blue-400',\n          bgColor: 'bg-blue-100 dark:bg-blue-900/30'\n        },\n        {\n          icon: '🌍',\n          value: Math.floor(Math.random() * 500) + 100,\n          label: 'زوار اليوم',\n          color: 'text-green-600 dark:text-green-400',\n          bgColor: 'bg-green-100 dark:bg-green-900/30'\n        },\n        {\n          icon: '📊',\n          value: Math.floor(Math.random() * 50000) + 20000,\n          label: 'المشاهدات',\n          color: 'text-purple-600 dark:text-purple-400',\n          bgColor: 'bg-purple-100 dark:bg-purple-900/30'\n        },\n        {\n          icon: '⭐',\n          value: Math.floor(Math.random() * 100) + 50,\n          label: 'التقييمات',\n          color: 'text-amber-600 dark:text-amber-400',\n          bgColor: 'bg-amber-100 dark:bg-amber-900/30'\n        }\n      ];\n      setStats(newStats);\n    };\n\n    fetchStats();\n    \n    // تحديث الإحصائيات كل 30 ثانية\n    const interval = setInterval(fetchStats, 30000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  const animateValue = (start: number, end: number, duration: number = 2000) => {\n    return new Promise((resolve) => {\n      const startTime = Date.now();\n      const timer = setInterval(() => {\n        const elapsed = Date.now() - startTime;\n        const progress = Math.min(elapsed / duration, 1);\n        const current = Math.floor(start + (end - start) * progress);\n        \n        if (progress >= 1) {\n          clearInterval(timer);\n          resolve(end);\n        }\n      }, 16);\n    });\n  };\n\n  if (!mounted) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            📈 إحصائيات الموقع\n          </h3>\n          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n        </div>\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n          {[1, 2, 3, 4].map((i) => (\n            <div key={i} className=\"text-center p-3 rounded-lg bg-gray-100 dark:bg-gray-700 animate-pulse\">\n              <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto mb-2\"></div>\n              <div className=\"h-6 bg-gray-300 dark:bg-gray-600 rounded mb-1\"></div>\n              <div className=\"h-4 bg-gray-300 dark:bg-gray-600 rounded\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300\"\n    >\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n          📈 إحصائيات الموقع\n        </h3>\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n          <span className=\"text-xs text-gray-500 dark:text-gray-400\">مباشر</span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n        {stats.map((stat, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5, delay: index * 0.1 }}\n            className={`text-center p-4 rounded-lg ${stat.bgColor} hover:scale-105 transition-all duration-300 cursor-pointer group relative overflow-hidden`}\n          >\n            {/* Background Animation */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"></div>\n\n            <div className=\"relative z-10\">\n              <div className=\"text-2xl mb-2 group-hover:scale-110 transition-transform duration-300\">\n                {stat.icon}\n              </div>\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 1, delay: 0.5 + index * 0.1 }}\n                className={`text-2xl font-bold ${stat.color} mb-1`}\n              >\n                {stat.value.toLocaleString()}\n              </motion.div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-300 font-medium\">\n                {stat.label}\n              </div>\n\n              {/* Mini Progress Bar */}\n              <div className=\"mt-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1\">\n                <motion.div\n                  initial={{ width: 0 }}\n                  animate={{ width: `${Math.min((stat.value / 15000) * 100, 100)}%` }}\n                  transition={{ duration: 2, delay: 1 + index * 0.1 }}\n                  className={`h-1 rounded-full ${stat.color.replace('text-', 'bg-')}`}\n                ></motion.div>\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Weekly Chart */}\n      <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          📈 إحصائيات الأسبوع\n        </h4>\n        <div className=\"flex items-end justify-between h-16 gap-1\">\n          {['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'].map((day, index) => {\n            const height = Math.random() * 60 + 20;\n            return (\n              <div key={day} className=\"flex flex-col items-center flex-1\">\n                <motion.div\n                  initial={{ height: 0 }}\n                  animate={{ height: `${height}%` }}\n                  transition={{ duration: 1, delay: 2 + index * 0.1 }}\n                  className=\"w-full bg-gradient-to-t from-amber-500 to-amber-300 rounded-t-sm min-h-[4px] mb-1\"\n                  title={`${day}: ${Math.floor(height * 10)} زائر`}\n                ></motion.div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 transform rotate-45 origin-bottom-left\">\n                  {day.slice(0, 2)}\n                </span>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n          <span>آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}</span>\n          <span className=\"flex items-center gap-1\">\n            <div className=\"w-1 h-1 bg-green-500 rounded-full animate-ping\"></div>\n            متصل\n          </span>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAC7C;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,6BAA6B;QAC7B,MAAM,aAAa;YACjB,MAAM,WAAW;gBACf;oBACE,MAAM;oBACN,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;oBAC3C,OAAO;oBACP,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;oBACzC,OAAO;oBACP,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;oBAC3C,OAAO;oBACP,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;oBACzC,OAAO;oBACP,OAAO;oBACP,SAAS;gBACX;aACD;YACD,SAAS;QACX;QAEA;QAEA,+BAA+B;QAC/B,MAAM,WAAW,YAAY,YAAY;QAEzC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC,OAAe,KAAa,WAAmB,IAAI;QACvE,OAAO,IAAI,QAAQ,CAAC;YAClB,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,QAAQ,YAAY;gBACxB,MAAM,UAAU,KAAK,GAAG,KAAK;gBAC7B,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU;gBAC9C,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI;gBAEnD,IAAI,YAAY,GAAG;oBACjB,cAAc;oBACd,QAAQ;gBACV;YACF,GAAG;QACL;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;4BAAY,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;2BAHP;;;;;;;;;;;;;;;;IASpB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8E;;;;;;kCAG5F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAI/D,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;wBAChD,WAAW,CAAC,2BAA2B,EAAE,KAAK,OAAO,CAAC,0FAA0F,CAAC;;0CAGjJ,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,UAAU;4CAAG,OAAO,MAAM,QAAQ;wCAAI;wCACpD,WAAW,CAAC,mBAAmB,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC;kDAEjD,KAAK,KAAK,CAAC,cAAc;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAIb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,AAAC,KAAK,KAAK,GAAG,QAAS,KAAK,KAAK,CAAC,CAAC;4CAAC;4CAClE,YAAY;gDAAE,UAAU;gDAAG,OAAO,IAAI,QAAQ;4CAAI;4CAClD,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,SAAS,QAAQ;;;;;;;;;;;;;;;;;;uBA/BpE;;;;;;;;;;0BAwCX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAS;4BAAS;4BAAW;4BAAY;4BAAY;4BAAU;yBAAS,CAAC,GAAG,CAAC,CAAC,KAAK;4BACnF,MAAM,SAAS,KAAK,MAAM,KAAK,KAAK;4BACpC,qBACE,8OAAC;gCAAc,WAAU;;kDACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;wCAAE;wCACrB,SAAS;4CAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;wCAAC;wCAChC,YAAY;4CAAE,UAAU;4CAAG,OAAO,IAAI,QAAQ;wCAAI;wCAClD,WAAU;wCACV,OAAO,GAAG,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC;;;;;;kDAElD,8OAAC;wCAAK,WAAU;kDACb,IAAI,KAAK,CAAC,GAAG;;;;;;;+BATR;;;;;wBAad;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAK;gCAAY,IAAI,OAAO,kBAAkB,CAAC;;;;;;;sCAChD,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;;;;;gCAAuD;;;;;;;;;;;;;;;;;;;;;;;;AAOlF", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Golden%20Eagle%20Company/project-showcase/src/components/AdvancedVisitorStats.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface StatCard {\n  title: string;\n  value: number;\n  change: number;\n  icon: string;\n  color: string;\n  bgGradient: string;\n}\n\nexport default function AdvancedVisitorStats() {\n  const [mounted, setMounted] = useState(false);\n  const [stats, setStats] = useState<StatCard[]>([]);\n\n  useEffect(() => {\n    setMounted(true);\n    \n    const generateStats = () => {\n      const newStats: StatCard[] = [\n        {\n          title: 'إجمالي الزوار',\n          value: Math.floor(Math.random() * 50000) + 25000,\n          change: Math.floor(Math.random() * 20) + 5,\n          icon: '👥',\n          color: 'text-blue-600 dark:text-blue-400',\n          bgGradient: 'from-blue-500 to-blue-600'\n        },\n        {\n          title: 'زوار اليوم',\n          value: Math.floor(Math.random() * 1000) + 200,\n          change: Math.floor(Math.random() * 15) + 2,\n          icon: '🌍',\n          color: 'text-green-600 dark:text-green-400',\n          bgGradient: 'from-green-500 to-green-600'\n        },\n        {\n          title: 'المشاهدات',\n          value: Math.floor(Math.random() * 100000) + 50000,\n          change: Math.floor(Math.random() * 25) + 8,\n          icon: '📊',\n          color: 'text-purple-600 dark:text-purple-400',\n          bgGradient: 'from-purple-500 to-purple-600'\n        },\n        {\n          title: 'معدل البقاء',\n          value: Math.floor(Math.random() * 30) + 120, // seconds\n          change: Math.floor(Math.random() * 10) + 3,\n          icon: '⏱️',\n          color: 'text-amber-600 dark:text-amber-400',\n          bgGradient: 'from-amber-500 to-amber-600'\n        }\n      ];\n      setStats(newStats);\n    };\n\n    generateStats();\n    const interval = setInterval(generateStats, 45000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  if (!mounted) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {[1, 2, 3, 4].map((i) => (\n          <div key={i} className=\"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg animate-pulse\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full\"></div>\n              <div className=\"w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded\"></div>\n            </div>\n            <div className=\"w-20 h-8 bg-gray-300 dark:bg-gray-600 rounded mb-2\"></div>\n            <div className=\"w-24 h-4 bg-gray-300 dark:bg-gray-600 rounded\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {stats.map((stat, index) => (\n        <motion.div\n          key={index}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: index * 0.1 }}\n          className=\"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 group\"\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${stat.bgGradient} flex items-center justify-center text-white text-xl group-hover:scale-110 transition-transform duration-300`}>\n              {stat.icon}\n            </div>\n            <div className={`text-sm font-medium px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400`}>\n              +{stat.change}%\n            </div>\n          </div>\n\n          {/* Value */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 1, delay: 0.5 + index * 0.1 }}\n            className={`text-3xl font-bold ${stat.color} mb-2`}\n          >\n            {stat.title === 'معدل البقاء' \n              ? `${Math.floor(stat.value / 60)}:${(stat.value % 60).toString().padStart(2, '0')}`\n              : stat.value.toLocaleString()\n            }\n          </motion.div>\n\n          {/* Title */}\n          <div className=\"text-gray-600 dark:text-gray-300 font-medium\">\n            {stat.title}\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${Math.min((stat.value / (stat.title === 'معدل البقاء' ? 300 : 100000)) * 100, 100)}%` }}\n              transition={{ duration: 2, delay: 1 + index * 0.1 }}\n              className={`h-2 rounded-full bg-gradient-to-r ${stat.bgGradient}`}\n            ></motion.div>\n          </div>\n\n          {/* Sparkline Effect */}\n          <div className=\"mt-3 flex items-end justify-between h-8 gap-1\">\n            {Array.from({ length: 7 }, (_, i) => {\n              const height = Math.random() * 100;\n              return (\n                <motion.div\n                  key={i}\n                  initial={{ height: 0 }}\n                  animate={{ height: `${height}%` }}\n                  transition={{ duration: 1, delay: 2 + i * 0.05 }}\n                  className={`w-1 bg-gradient-to-t ${stat.bgGradient} rounded-full opacity-60`}\n                ></motion.div>\n              );\n            })}\n          </div>\n        </motion.div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAce,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,MAAM,gBAAgB;YACpB,MAAM,WAAuB;gBAC3B;oBACE,OAAO;oBACP,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;oBAC3C,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACzC,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;oBAC1C,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACzC,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU;oBAC5C,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACzC,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACxC,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACzC,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;aACD;YACD,SAAS;QACX;QAEA;QACA,MAAM,WAAW,YAAY,eAAe;QAE5C,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;mBANP;;;;;;;;;;IAWlB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,sCAAsC,EAAE,KAAK,UAAU,CAAC,4GAA4G,CAAC;0CACnL,KAAK,IAAI;;;;;;0CAEZ,8OAAC;gCAAI,WAAW,CAAC,+GAA+G,CAAC;;oCAAE;oCAC/H,KAAK,MAAM;oCAAC;;;;;;;;;;;;;kCAKlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAG,OAAO,MAAM,QAAQ;wBAAI;wBACpD,WAAW,CAAC,mBAAmB,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC;kCAEjD,KAAK,KAAK,KAAK,gBACZ,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,GAAG,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM,GACjF,KAAK,KAAK,CAAC,cAAc;;;;;;kCAK/B,8OAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,AAAC,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,gBAAgB,MAAM,MAAM,IAAK,KAAK,KAAK,CAAC,CAAC;4BAAC;4BAC1G,YAAY;gCAAE,UAAU;gCAAG,OAAO,IAAI,QAAQ;4BAAI;4BAClD,WAAW,CAAC,kCAAkC,EAAE,KAAK,UAAU,EAAE;;;;;;;;;;;kCAKrE,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,CAAC,GAAG;4BAC7B,MAAM,SAAS,KAAK,MAAM,KAAK;4BAC/B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,QAAQ;gCAAE;gCACrB,SAAS;oCAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;gCAAC;gCAChC,YAAY;oCAAE,UAAU;oCAAG,OAAO,IAAI,IAAI;gCAAK;gCAC/C,WAAW,CAAC,qBAAqB,EAAE,KAAK,UAAU,CAAC,wBAAwB,CAAC;+BAJvE;;;;;wBAOX;;;;;;;eAzDG;;;;;;;;;;AA+Df", "debugId": null}}]}