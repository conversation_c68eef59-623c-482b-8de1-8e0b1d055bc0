import Link from 'next/link';
import { useParams } from 'next/navigation';

export default async function AboutPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  const teamMembers = [
    {
      name: 'أحمد محمد',
      nameEn: '<PERSON>',
      role: 'مطور تطبيقات',
      roleEn: 'Mobile App Developer',
      image: '/api/placeholder/150/150',
      bio: 'خبرة 5 سنوات في تطوير التطبيقات',
      bioEn: '5 years experience in mobile app development'
    },
    {
      name: 'سارة أحمد',
      nameEn: '<PERSON>',
      role: 'مطورة مواقع',
      roleEn: 'Web Developer',
      image: '/api/placeholder/150/150',
      bio: 'متخصصة في تطوير المواقع الحديثة',
      bioEn: 'Specialized in modern web development'
    },
    {
      name: 'علي حسن',
      nameEn: '<PERSON>',
      role: 'مصمم UI/UX',
      roleEn: 'UI/UX Designer',
      image: '/api/placeholder/150/150',
      bio: 'مصمم واجهات مستخدم إبداعية',
      bioEn: 'Creative user interface designer'
    }
  ];

  const stats = [
    {
      number: '50+',
      label: locale === 'ar' ? 'مشروع مكتمل' : 'Completed Projects',
      icon: '🚀'
    },
    {
      number: '100+',
      label: locale === 'ar' ? 'عميل راضي' : 'Happy Clients',
      icon: '😊'
    },
    {
      number: '3+',
      label: locale === 'ar' ? 'سنوات خبرة' : 'Years Experience',
      icon: '⭐'
    },
    {
      number: '24/7',
      label: locale === 'ar' ? 'دعم فني' : 'Technical Support',
      icon: '🛠️'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">
                  Project Showcase
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/${locale}`}
                className="text-blue-600 hover:text-blue-700 transition-colors"
              >
                ← {locale === 'ar' ? 'العودة للرئيسية' : 'Back to Home'}
              </Link>
              <Link
                href={locale === 'en' ? '/ar/about' : '/en/about'}
                className="p-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                {locale === 'en' ? '🇮🇶 العربية' : '🇺🇸 English'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {locale === 'ar' ? 'من نحن' : 'About Us'}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {locale === 'ar' 
                ? 'نحن فريق من المطورين والمصممين المتخصصين في إنشاء تطبيقات ومواقع ويب عالية الجودة'
                : 'We are a team of developers and designers specialized in creating high-quality applications and websites'
              }
            </p>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  {locale === 'ar' ? 'قصتنا' : 'Our Story'}
                </h2>
                <div className="space-y-4 text-gray-600">
                  <p>
                    {locale === 'ar' 
                      ? 'بدأت رحلتنا في عام 2021 بهدف تقديم حلول تقنية مبتكرة للشركات والأفراد في العراق والمنطقة العربية. نؤمن بأن التكنولوجيا يجب أن تكون في متناول الجميع وأن تساهم في تطوير المجتمع.'
                      : 'Our journey began in 2021 with the goal of providing innovative technical solutions for companies and individuals in Iraq and the Arab region. We believe that technology should be accessible to everyone and contribute to community development.'
                    }
                  </p>
                  <p>
                    {locale === 'ar' 
                      ? 'نتخصص في تطوير التطبيقات المحمولة والمواقع الإلكترونية باستخدام أحدث التقنيات والأدوات. فريقنا مكون من مطورين ومصممين ذوي خبرة واسعة في مجال التكنولوجيا.'
                      : 'We specialize in developing mobile applications and websites using the latest technologies and tools. Our team consists of developers and designers with extensive experience in the technology field.'
                    }
                  </p>
                  <p>
                    {locale === 'ar' 
                      ? 'هدفنا هو مساعدة عملائنا على تحقيق أهدافهم من خلال تقديم حلول تقنية مخصصة تلبي احتياجاتهم الفريدة.'
                      : 'Our goal is to help our clients achieve their objectives by providing customized technical solutions that meet their unique needs.'
                    }
                  </p>
                </div>
              </div>
              <div className="bg-gray-200 rounded-lg aspect-video flex items-center justify-center">
                <span className="text-gray-500 text-4xl">🏢</span>
              </div>
            </div>
          </div>
        </section>

        {/* Stats */}
        <section className="py-20 bg-blue-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
              {stats.map((stat, index) => (
                <div key={index}>
                  <div className="text-4xl mb-2">{stat.icon}</div>
                  <div className="text-3xl font-bold mb-2">{stat.number}</div>
                  <div className="text-lg opacity-90">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Team */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {locale === 'ar' ? 'فريقنا' : 'Our Team'}
              </h2>
              <p className="text-xl text-gray-600">
                {locale === 'ar' 
                  ? 'تعرف على الأشخاص الذين يقفون وراء نجاحنا'
                  : 'Meet the people behind our success'
                }
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <div key={index} className="text-center">
                  <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-gray-500 text-4xl">👤</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {locale === 'ar' ? member.name : member.nameEn}
                  </h3>
                  <p className="text-blue-600 font-medium mb-2">
                    {locale === 'ar' ? member.role : member.roleEn}
                  </p>
                  <p className="text-gray-600">
                    {locale === 'ar' ? member.bio : member.bioEn}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Mission & Vision */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-6">
                  <span className="text-white text-2xl">🎯</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {locale === 'ar' ? 'مهمتنا' : 'Our Mission'}
                </h3>
                <p className="text-gray-600">
                  {locale === 'ar' 
                    ? 'تقديم حلول تقنية مبتكرة وعالية الجودة تساعد عملائنا على النجاح في العصر الرقمي وتحقيق أهدافهم التجارية.'
                    : 'Providing innovative and high-quality technical solutions that help our clients succeed in the digital age and achieve their business goals.'
                  }
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-6">
                  <span className="text-white text-2xl">🔮</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {locale === 'ar' ? 'رؤيتنا' : 'Our Vision'}
                </h3>
                <p className="text-gray-600">
                  {locale === 'ar' 
                    ? 'أن نكون الشركة الرائدة في تطوير التطبيقات والمواقع في المنطقة العربية، ونساهم في التحول الرقمي للمجتمع.'
                    : 'To be the leading company in application and website development in the Arab region, contributing to the digital transformation of society.'
                  }
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact CTA */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'هل لديك مشروع في ذهنك؟' : 'Have a project in mind?'}
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              {locale === 'ar' 
                ? 'نحن هنا لمساعدتك في تحويل فكرتك إلى واقع'
                : "We're here to help you turn your idea into reality"
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href={`/${locale}/contact`}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                {locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}
              </Link>
              <Link
                href={`/${locale}/projects`}
                className="border border-blue-600 text-blue-600 px-8 py-3 rounded-lg hover:bg-blue-50 transition-colors font-medium"
              >
                {locale === 'ar' ? 'تصفح مشاريعنا' : 'View Our Projects'}
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600">
            © 2024 Project Showcase. {locale === 'ar' ? 'جميع الحقوق محفوظة' : 'All rights reserved'}.
          </p>
        </div>
      </footer>
    </div>
  );
}
