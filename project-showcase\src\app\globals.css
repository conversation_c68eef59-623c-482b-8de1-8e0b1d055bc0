@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --toast-bg: #ffffff;
  --toast-color: #171717;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #1e293b;
  --secondary-foreground: #f8fafc;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #1e293b;
  --accent-foreground: #f8fafc;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #3b82f6;
  --toast-bg: #1e293b;
  --toast-color: #f8fafc;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Smooth transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark mode specific improvements */
:root {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}

/* Ensure proper contrast in dark mode */
.dark input,
.dark textarea,
.dark select {
  background-color: rgb(31 41 55); /* gray-800 */
  border-color: rgb(75 85 99); /* gray-600 */
  color: rgb(243 244 246); /* gray-100 */
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: rgb(251 191 36); /* amber-400 */
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
}

/* Dark mode improvements for better visibility */
.dark {
  /* Better text contrast */
  --tw-prose-body: rgb(209 213 219); /* gray-300 */
  --tw-prose-headings: rgb(243 244 246); /* gray-100 */
  --tw-prose-links: rgb(251 191 36); /* amber-400 */
  --tw-prose-bold: rgb(243 244 246); /* gray-100 */
  --tw-prose-counters: rgb(156 163 175); /* gray-400 */
  --tw-prose-bullets: rgb(75 85 99); /* gray-600 */
  --tw-prose-hr: rgb(55 65 81); /* gray-700 */
  --tw-prose-quotes: rgb(209 213 219); /* gray-300 */
  --tw-prose-quote-borders: rgb(75 85 99); /* gray-600 */
  --tw-prose-captions: rgb(156 163 175); /* gray-400 */
  --tw-prose-code: rgb(243 244 246); /* gray-100 */
  --tw-prose-pre-code: rgb(209 213 219); /* gray-300 */
  --tw-prose-pre-bg: rgb(17 24 39); /* gray-900 */
  --tw-prose-th-borders: rgb(75 85 99); /* gray-600 */
  --tw-prose-td-borders: rgb(55 65 81); /* gray-700 */
}

/* Enhanced scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: rgb(31 41 55); /* gray-800 */
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(75 85 99); /* gray-600 */
  border-radius: 4px;
  border: 1px solid rgb(55 65 81); /* gray-700 */
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128); /* gray-500 */
}

.dark ::-webkit-scrollbar-corner {
  background: rgb(31 41 55); /* gray-800 */
}

/* Selection colors for dark mode */
.dark ::selection {
  background-color: rgba(251, 191, 36, 0.3); /* amber with opacity */
  color: rgb(243 244 246); /* gray-100 */
}

/* Better focus rings for dark mode */
.dark *:focus {
  outline-color: rgb(251 191 36); /* amber-400 */
}

/* Improved shadows for dark mode */
.dark .shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.dark .shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

.dark .shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}
